name: Deploy to Development

on:
  push:
    branches: [ develop ]
  pull_request_target:
    branches: [ develop ]
    types: [closed]

env:
  NODE_VERSION: '22'
  FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

jobs:
  deploy:
    name: Deploy to Development
    runs-on: ubuntu-latest
    environment: development
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'

      - name: Enable Corepack
        run: corepack enable

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build application
        run: yarn run build:development
        env:
          NODE_ENV: development
          VITE_APP_ENV: development

      - name: Install and Deploy to Firebase
        run: |
          npm install -g firebase-tools@latest
          firebase use aida-22a9a
          firebase deploy --token "${{ env.FIREBASE_TOKEN }}" --non-interactive
        env:
          FIREBASE_TOKEN: ${{ env.FIREBASE_TOKEN }} 