import { apiService } from '..';
import { noteApiConfigs } from './configs';

import type { Note } from './types';
import type { NoteConfigParams } from './configs';

// ----------------------------------------------------------------------

export const notesService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getNotes: build.query<Note[], {}>({
      query: noteApiConfigs.getNotes,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Notes' as const, id })),
              { type: 'Notes', id: 'LIST' },
            ]
          : [{ type: 'Notes', id: 'LIST' }],
    }),
    getNotesByResource: build.query<Note[], NoteConfigParams<'getNotesByResource'>>({
      query: noteApiConfigs.getNotesByResource,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Notes' as const, id })),
              { type: 'Notes', id: 'LIST' },
            ]
          : [{ type: 'Notes', id: 'LIST' }],
    }),
    getNotesByProject: build.query<Note[], NoteConfigParams<'getNotesByProject'>>({
      query: noteApiConfigs.getNotesByProject,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Notes' as const, id })),
              { type: 'Notes', id: 'LIST' },
            ]
          : [{ type: 'Notes', id: 'LIST' }],
    }),
    getNote: build.query<Note, NoteConfigParams<'getNote'>>({
      query: noteApiConfigs.getNote,
      providesTags: (result) => (result ? [{ type: 'Notes' as const, id: result.id }] : []),
    }),
    createNote: build.mutation<Note, NoteConfigParams<'createNote'>>({
      query: noteApiConfigs.createNote,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;

          // Add to global notes list
          dispatch(
            notesService.util.updateQueryData('getNotes', {}, (draft) => {
              draft.unshift(data); // Add to beginning (most recent first)
            })
          );

          // Add to resource-specific notes if applicable
          if (data.resourceId) {
            dispatch(
              notesService.util.updateQueryData(
                'getNotesByResource',
                { resourceId: data.resourceId },
                (draft) => {
                  draft.unshift(data);
                }
              )
            );
          }

          // Add to project-specific notes if applicable
          if (data.projectId) {
            dispatch(
              notesService.util.updateQueryData(
                'getNotesByProject',
                { projectId: data.projectId },
                (draft) => {
                  draft.unshift(data);
                }
              )
            );
          }
        } catch {
          // Error handling - optimistic updates will be automatically reverted
        }
      },
    }),
    updateNote: build.mutation<Note, NoteConfigParams<'updateNote'>>({
      query: noteApiConfigs.updateNote,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;

          // Update in global notes list
          dispatch(
            notesService.util.updateQueryData('getNotes', {}, (draft) => {
              const index = draft.findIndex((note) => note.id === data.id);
              if (index !== -1) {
                draft[index] = data;
              }
            })
          );

          // Update in resource-specific notes if applicable
          if (data.resourceId) {
            dispatch(
              notesService.util.updateQueryData(
                'getNotesByResource',
                { resourceId: data.resourceId },
                (draft) => {
                  const index = draft.findIndex((note) => note.id === data.id);
                  if (index !== -1) {
                    draft[index] = data;
                  }
                }
              )
            );
          }

          // Update in project-specific notes if applicable
          if (data.projectId) {
            dispatch(
              notesService.util.updateQueryData(
                'getNotesByProject',
                { projectId: data.projectId },
                (draft) => {
                  const index = draft.findIndex((note) => note.id === data.id);
                  if (index !== -1) {
                    draft[index] = data;
                  }
                }
              )
            );
          }

          // Update individual note cache
          dispatch(notesService.util.updateQueryData('getNote', { id: data.id }, () => data));
        } catch {
          // Error handling - optimistic updates will be automatically reverted
        }
      },
    }),
    deleteNote: build.mutation<void, NoteConfigParams<'deleteNote'>>({
      query: noteApiConfigs.deleteNote,
      async onQueryStarted(arg, { dispatch, queryFulfilled, getState }) {
        const patchResults = [];

        // Remove from global notes list
        const globalPatch = dispatch(
          notesService.util.updateQueryData('getNotes', {}, (draft) =>
            draft.filter((item) => item.id !== arg.id)
          )
        );
        patchResults.push(globalPatch);

        // Remove from all resource-specific note caches
        const state = getState() as any;
        const queries = state.api.queries;

        Object.keys(queries).forEach((queryKey) => {
          if (queryKey.startsWith('getNotesByResource(')) {
            const query = queries[queryKey];
            if (query?.data?.some((note: any) => note.id === arg.id)) {
              const resourceMatch = queryKey.match(/resourceId":"([^"]+)"/);
              if (resourceMatch) {
                const resourceId = resourceMatch[1];
                const patch = dispatch(
                  notesService.util.updateQueryData('getNotesByResource', { resourceId }, (draft) =>
                    draft.filter((item) => item.id !== arg.id)
                  )
                );
                patchResults.push(patch);
              }
            }
          }

          if (queryKey.startsWith('getNotesByProject(')) {
            const query = queries[queryKey];
            if (query?.data?.some((note: any) => note.id === arg.id)) {
              const projectMatch = queryKey.match(/projectId":"([^"]+)"/);
              if (projectMatch) {
                const projectId = projectMatch[1];
                const patch = dispatch(
                  notesService.util.updateQueryData('getNotesByProject', { projectId }, (draft) =>
                    draft.filter((item) => item.id !== arg.id)
                  )
                );
                patchResults.push(patch);
              }
            }
          }
        });

        try {
          await queryFulfilled;
        } catch {
          // Revert all optimistic updates on error
          patchResults.forEach((patch) => patch.undo());
        }
      },
    }),
  }),
  overrideExisting: true,
});
