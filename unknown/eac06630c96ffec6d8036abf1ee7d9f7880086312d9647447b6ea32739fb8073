import type { Draft } from 'immer';
import type { ThunkDispatch } from '@reduxjs/toolkit';

import { notesService } from './service';

import type { Note } from './types';

export const optimisticAddNoteToList = (notes: Draft<Note[]>, newNote: Note) => {
  // Add to the beginning of the list (most recent first)
  notes.unshift(newNote);
};

export const optimisticUpdateNoteInList = (notes: Draft<Note[]>, updatedNote: Note) => {
  const index = notes.findIndex((note) => note.id === updatedNote.id);
  if (index !== -1) {
    notes[index] = updatedNote;
  }
};

export const optimisticRemoveNoteFromList = (notes: Draft<Note[]>, noteId: string) => {
  const index = notes.findIndex((note) => note.id === noteId);
  if (index !== -1) {
    notes.splice(index, 1);
  }
};

export const optimisticAddNote = (
  dispatch: ThunkDispatch<any, any, any>,
  newNote: Note
) => {
  // Add to global notes list
  dispatch(
    notesService.util.updateQueryData('getNotes', {}, (draft) =>
      optimisticAddNoteToList(draft, newNote)
    )
  );

  // Add to resource-specific notes if applicable
  if (newNote.resourceId) {
    dispatch(
      notesService.util.updateQueryData(
        'getNotesByResource',
        { resourceId: newNote.resourceId },
        (draft) => optimisticAddNoteToList(draft, newNote)
      )
    );
  }

  // Add to project-specific notes if applicable
  if (newNote.projectId) {
    dispatch(
      notesService.util.updateQueryData(
        'getNotesByProject',
        { projectId: newNote.projectId },
        (draft) => optimisticAddNoteToList(draft, newNote)
      )
    );
  }
};

export const optimisticUpdateNote = (
  dispatch: ThunkDispatch<any, any, any>,
  updatedNote: Note
) => {
  // Update in global notes list
  dispatch(
    notesService.util.updateQueryData('getNotes', {}, (draft) =>
      optimisticUpdateNoteInList(draft, updatedNote)
    )
  );

  // Update in resource-specific notes if applicable
  if (updatedNote.resourceId) {
    dispatch(
      notesService.util.updateQueryData(
        'getNotesByResource',
        { resourceId: updatedNote.resourceId },
        (draft) => optimisticUpdateNoteInList(draft, updatedNote)
      )
    );
  }

  // Update in project-specific notes if applicable
  if (updatedNote.projectId) {
    dispatch(
      notesService.util.updateQueryData(
        'getNotesByProject',
        { projectId: updatedNote.projectId },
        (draft) => optimisticUpdateNoteInList(draft, updatedNote)
      )
    );
  }

  // Update individual note cache
  dispatch(
    notesService.util.updateQueryData('getNote', { id: updatedNote.id }, () => updatedNote)
  );
};
