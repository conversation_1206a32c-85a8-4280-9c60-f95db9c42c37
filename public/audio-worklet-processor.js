/**
 * AudioWorklet processor for high-performance audio processing
 * This replaces the deprecated ScriptProcessorNode for better performance
 */
class LiveTranscriptionAudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.bufferSize = 4096;
    this.buffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;
    this.sampleRate = 16000; // Match the recording sample rate
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0];
    
    if (input.length > 0) {
      const channelData = input[0];
      let sum = 0;
      
      // Buffer the audio data for more efficient transmission
      for (let i = 0; i < channelData.length; i++) {
        const sample = channelData[i];
        this.buffer[this.bufferIndex] = sample;
        this.bufferIndex++;
        
        // Calculate RMS for audio level monitoring
        sum += sample * sample;
        
        if (this.bufferIndex >= this.bufferSize) {
          // Calculate audio level (RMS)
          const audioLevel = Math.sqrt(sum / this.bufferSize);
          
          // Send buffered audio data as ArrayBuffer for better performance
          const audioBuffer = new ArrayBuffer(this.buffer.length * 4);
          const view = new Float32Array(audioBuffer);
          view.set(this.buffer);
          
          this.port.postMessage({
            type: 'audioData',
            data: audioBuffer,
            audioLevel: audioLevel
          }, [audioBuffer]); // Transfer ownership for zero-copy
          
          this.bufferIndex = 0;
          sum = 0;
        }
      }
    }
    
    return true;
  }
}

registerProcessor('audio-worklet-processor', LiveTranscriptionAudioProcessor);
