import type { CancelTokenSource } from 'axios';
import type { GetResourceUploadUrlResponse } from 'src/store/api/resources';

import type { Transcription, TranscriptStatus } from './transcription';

export interface ResourceTopic {
  topic_name: string;
  score: number;
}

export interface ResourcePermission {
  canView: boolean;
  canEdit: boolean;
}

export interface Resource {
  id: string;
  ffprobe: any;
  duration: number;
  thumbnailUrl: string;
  fileSize: number;
  fileName: string;
  name: string;
  createdById: string;
  url: string;
  createdAt: Date;
  fileLastModified: Date;
  orgId?: string;
  sessionId?: string;
  topics?: ResourceTopic[];
  transcription?: Transcription[];
  transcriptionJobStatus?: TranscriptStatus;
  projectId?: string;
  folderId?: string;
  folder?: {
    id: string;
    name: string;
  };
  project?: {
    id: string;
    name: string;
  };
  userPermissions?: ResourcePermission;
  isTranscoding?: boolean;
}

export interface ResourceUploadQueueItem {
  id: string;
  file: File;
  uploadUrlData: GetResourceUploadUrlResponse;
  progress?: number;
  status?: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed' | 'cancelled';
  submittedAt?: Date;
  cancelToken?: CancelTokenSource;
  projectId?: string;
  folderId?: string;
  transcoded?: boolean;
  mode?: 'delete-original' | 'keep-both';
}

// APIs (will be migrated to RTKQ later)
export interface CreateResourceRequest {
  fileName: string;
  uploadedFileName: string;
  fileSize: number;
}
