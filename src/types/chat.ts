import type { IDateValue } from './common';

// ----------------------------------------------------------------------

export type IChatMessage = {
  id: string;
  body: string | React.ReactNode;
  senderId: string;
  createdAt: IDateValue;
};

export type IChatParticipant = {
  id: string;
  name: string;
  avatar?: React.ReactNode;
  avatarUrl?: string;
};

export type IChatConversation = {
  id: string;
  messages: IChatMessage[];
  participants: IChatParticipant[];
};

export type IChatConversations = {
  allIds: string[];
  byId: Record<string, IChatConversation>;
};
