export interface LiveTranscriptionJwtResponse {
  jwt: string;
}

export type AudioSource = 'speaker' | 'microphone';

export interface LiveTranscriptionSegment {
  id: string;
  text: string;
  confidence: number;
  startTime: number;
  endTime: number;
  isFinal: boolean;
  speakerId?: string;
  timestamp: Date;
  audioSource?: AudioSource;
}

export interface LiveTranscriptionConfig {
  language: string;
  model?: string;
  enablePartialResults?: boolean;
  maxAlternatives?: number;
  enableSpeakerDiarization?: boolean;
  enableProfanityFilter?: boolean;
  audioQuality?: 'low' | 'medium' | 'high';
  isDualMode?: boolean;
}

export interface DualStreamState {
  isCapturingScreen: boolean;
  speakerConnected: boolean;
  microphoneConnected: boolean;
  speakerConnectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  microphoneConnectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  speakerError: string | null;
  microphoneError: string | null;
}

export interface LiveTranscriptionState {
  isConnected: boolean;
  isRecording: boolean;
  isTranscribing: boolean;
  segments: LiveTranscriptionSegment[];
  error: string | null;
  connectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  audioLevel?: number;
  latency?: number;
  performanceMetrics?: {
    audioPacketsSent: number;
    audioPacketsLost: number;
    averageLatency: number;
  };
  qualityMetrics?: {
    isSpeaking: boolean;
    backgroundNoiseLevel: number;
  };
  dualStream?: DualStreamState;
  isDualMode?: boolean;
}

export enum LiveTranscriptionSocketState {
  Disconnected = 'disconnected',
  Connecting = 'connecting',
  Connected = 'connected',
  Error = 'error',
}

export interface UseLiveTranscriptionReturn {
  state: LiveTranscriptionState;
  startTranscription: (config: LiveTranscriptionConfig) => Promise<void>;
  stopTranscription: () => Promise<void>;
  clearTranscription: () => void;
  exportTranscription: () => string;
  bootstrap: () => Promise<void>;
  audioDevices: MediaDeviceInfo[];
  selectedAudioDeviceId?: string;
  switchAudioDevice: (deviceId: string) => Promise<void>;
  getAudioLevel: () => number;
  getPerformanceMetrics: () => {
    audioPacketsSent: number;
    audioPacketsLost: number;
    averageLatency: number;
  };
  getQualityMetrics: () => {
    isSpeaking: boolean;
    backgroundNoiseLevel: number;
  };
  startDualTranscription: (config: LiveTranscriptionConfig) => Promise<void>;
  stopDualTranscription: () => Promise<void>;
  startScreenCapture: () => Promise<void>;
  stopScreenCapture: () => Promise<void>;
  getSegmentsBySource: (source?: AudioSource) => LiveTranscriptionSegment[];
  exportTranscriptionBySource: (source?: AudioSource) => string;
}
