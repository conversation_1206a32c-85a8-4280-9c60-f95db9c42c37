import type { RecallBotStatus } from './recall';

export enum SessionStatus {
  NotStarted = 'NotStarted',
  InProgress = 'InProgress',
  Processing = 'Processing',
  Completed = 'Completed',
  Failed = 'Failed',
}

export interface Session {
  id: string;
  createdById: string;
  meetingUrl: string;
  title: string;
  description: string;
  createdAt: Date;
  startTime: Date;
  endTime: Date;
  status: SessionStatus;
  recallBotStatus: RecallBotStatus;
}
