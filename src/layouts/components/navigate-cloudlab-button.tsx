import { m } from 'framer-motion';

import { Tooltip, IconButton } from '@mui/material';
import VideocamRoundedIcon from '@mui/icons-material/VideocamRounded';

import { CONFIG } from 'src/global-config';

import { varTap, varHover, transitionTap } from 'src/components/animate';

const NavigateCloudlabButton = () => {
  const goToCloudLab = () => {
    window.open(CONFIG.cloudLabUrl, '_self');
  };

  return (
    <Tooltip title="Go to CloudLab" arrow>
      <IconButton
        component={m.button}
        whileTap={varTap(0.96)}
        whileHover={varHover(1.04)}
        transition={transitionTap()}
        aria-label="Go to CloudLab"
        onClick={goToCloudLab}
      >
        <VideocamRoundedIcon fontSize="medium" />
      </IconButton>
    </Tooltip>
  );
};

export default NavigateCloudlabButton;
