import type { IconButtonProps } from '@mui/material/IconButton';

import Button from '@mui/material/Button';

import useFeatureFlags from 'src/hooks/feature-flags';
import useUserInitialContext from 'src/hooks/user-initial-context';

import { AppFeatures } from 'src/types';

import { useInviteContext } from 'src/sections/projects/components/invite-dialog/context';

// ----------------------------------------------------------------------

export function InviteButton({ sx, ...other }: IconButtonProps) {
  const { currentProject } = useUserInitialContext();
  const { isFlagEnabled } = useFeatureFlags();
  const hasProjectCollaboration = isFlagEnabled(AppFeatures.PROJECT_COLLABORATION);
  const invite = useInviteContext();

  const allowShare =
    hasProjectCollaboration && currentProject?.isOwner && !currentProject?.isDefault;

  if (!allowShare) return null;

  return (
    <Button variant="outlined" color="inherit" onClick={invite.onOpen}>
      Share
    </Button>
  );
}
