import type { IconButtonProps } from '@mui/material/IconButton';

import { useBoolean } from 'minimal-shared/hooks';

import Button from '@mui/material/Button';

import useFeatureFlags from 'src/hooks/feature-flags';
import useUserInitialContext from 'src/hooks/user-initial-context';

import { AppFeatures } from 'src/types';

import { Iconify } from 'src/components/iconify';

import LeaveProjectDialog from 'src/sections/projects/components/dialogs/leave-dialog';

// ----------------------------------------------------------------------

export function LeaveProjectButton({ sx, ...other }: IconButtonProps) {
  const { currentProject } = useUserInitialContext();
  const { isFlagEnabled } = useFeatureFlags();
  const hasProjectCollaboration = isFlagEnabled(AppFeatures.PROJECT_COLLABORATION);
  const leaveProjectDialog = useBoolean();

  const allowLeave =
    hasProjectCollaboration &&
    currentProject &&
    !currentProject.isOwner &&
    !currentProject.isDefault;

  if (!allowLeave) return null;

  return (
    <>
      <Button
        variant="outlined"
        color="inherit"
        onClick={leaveProjectDialog.onTrue}
        startIcon={<Iconify icon="solar:logout-2-bold" />}
      >
        Leave Project
      </Button>

      <LeaveProjectDialog
        open={leaveProjectDialog.value}
        onClose={leaveProjectDialog.onFalse}
        project={currentProject}
      />
    </>
  );
}
