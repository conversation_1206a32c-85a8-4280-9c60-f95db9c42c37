import type { IconButtonProps } from '@mui/material/IconButton';

import { m } from 'framer-motion';
import { useState, useCallback } from 'react';
import { usePopover } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { FlagIcon } from 'src/components/flag-icon';
import { CustomPopover } from 'src/components/custom-popover';
import { varTap, varHover, transitionTap } from 'src/components/animate';

// ----------------------------------------------------------------------

const REGIONS = [
  {
    value: 'eu',
    label: 'EU',
    countryCode: 'EU',
  },
] as const;

export type RegionPopoverProps = IconButtonProps;

export function RegionPopover({ sx, ...other }: RegionPopoverProps) {
  const { open, anchorEl, onClose, onOpen } = usePopover();
  const [selectedRegion, setSelectedRegion] = useState<string>(REGIONS[0].value);

  const handleChangeRegion = useCallback(
    (newRegion: string) => {
      setSelectedRegion(newRegion);
      onClose();
    },
    [onClose]
  );

  const currentRegion = REGIONS.find((region) => region.value === selectedRegion);

  const renderMenuList = () => (
    <CustomPopover open={open} anchorEl={anchorEl} onClose={onClose}>
      <MenuList sx={{ width: 160, minHeight: 72 }}>
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
            Region
          </Typography>
        </Box>
        {REGIONS?.map((option) => (
          <MenuItem
            key={option.value}
            selected={option.value === selectedRegion}
            onClick={() => handleChangeRegion(option.value)}
          >
            <FlagIcon code={option.countryCode} />
            {option.label}
          </MenuItem>
        ))}
      </MenuList>
    </CustomPopover>
  );

  return (
    <>
      <IconButton
        component={m.button}
        whileTap={varTap(0.96)}
        whileHover={varHover(1.04)}
        transition={transitionTap()}
        aria-label="Regions button"
        onClick={onOpen}
        sx={[
          (theme) => ({
            p: 0,
            width: 40,
            height: 40,
            ...(open && { bgcolor: theme.vars.palette.action.selected }),
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
        {...other}
      >
        <FlagIcon code={currentRegion?.countryCode} />
      </IconButton>

      {renderMenuList()}
    </>
  );
}
