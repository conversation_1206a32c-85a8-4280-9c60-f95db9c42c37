import posthog from 'posthog-js';
import { persistStore } from 'redux-persist';
import { createRoot } from 'react-dom/client';
import { PostHogProvider } from 'posthog-js/react';
import { He<PERSON><PERSON><PERSON>rovider } from 'react-helmet-async';
import { PersistGate } from 'redux-persist/integration/react';
import { Outlet, RouterProvider, createBrowserRouter } from 'react-router';

import App from './app';
import initSentry from './lib/sentry';
import { CONFIG } from './global-config';
import { routesSection } from './routes/sections';
import { getStore, configureAppStore } from './store';
import ErrorBoundary from './components/error-boundary';

posthog.init(CONFIG.posthog.key, {
  api_host: CONFIG.posthog.host,
  person_profiles: 'identified_only',
  autocapture: {
    css_selector_allowlist: ['[ph-autocapture]'], // Only track DOM events with ph-autocapture attribute
  },
});

// ----------------------------------------------------------------------

const router = createBrowserRouter([
  {
    Component: () => {
      const store = getStore();
      const persistor = persistStore(store);

      return (
        <App store={store}>
          <PersistGate loading={null} persistor={persistor}>
            <ErrorBoundary>
              <Outlet />
            </ErrorBoundary>
          </PersistGate>
        </App>
      );
    },
    children: routesSection,
    hasErrorBoundary: true,
  },
]);

const render = () => {
  const root = createRoot(document.getElementById('root')!);

  root.render(
    <PostHogProvider client={posthog}>
      <HelmetProvider>
        <RouterProvider router={router} />
      </HelmetProvider>
    </PostHogProvider>
  );
};

const renderApp = () => {
  // 1. Configure store
  configureAppStore();

  // 2. Initialize Sentry
  initSentry();

  // 3. Render app
  render();
};

renderApp();
