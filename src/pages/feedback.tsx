import type { UserFeedbackEntityType } from 'src/store/api/user-feedback/types';

import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { useSearchParams } from 'src/routes/hooks';

import { CONFIG } from 'src/global-config';
import { UserFeedbackRating } from 'src/store/api/user-feedback/types';
import { useSubmitFeedbackMutation } from 'src/store/api/user-feedback/hooks';

import { LoadingScreen } from 'src/components/loading-screen';
import { CustomAlert } from 'src/components/alert/custom-alert';
import FeedbackDetailsForm from 'src/components/feedback-detail/feedback-detail';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Feedback` };


export default function FeedbackPage() {
  const searchParams = useSearchParams();

  // Get the invitation code and projectId from URL parameters
  const entityId = searchParams.get('entityId');
  const entityType = searchParams.get('entityType') as UserFeedbackEntityType;
  const userId = searchParams.get('userId');
  const rating = searchParams.get('rating') as UserFeedbackRating;

  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const [submitFeedback, { isLoading }] = useSubmitFeedbackMutation();

  useEffect(() => {
    const handleFeedback = async () => {
      if (!entityId || !entityType || !userId || !rating) {
        setStatus('error');
        setErrorMessage('Invalid resource link. Missing required parameters.');
        return;
      }
      try {
        await submitFeedback({
          payload: {
            userId,
            rating,
            entityId,
            entityType,
          },
        }).unwrap();
        setStatus('success');
      } catch (error) {
        setStatus('error');
        setErrorMessage(
          'Failed to submit feedback. The resource may have expired or been deleted.'
        );
        console.error('Error submitting feedback:', error);
      }
    };
    handleFeedback();
  }, [entityId, entityType, userId, rating, submitFeedback]);

  if (status === 'loading' || isLoading) {
    return <LoadingScreen />;
  }

  // Show details page if thumb down and feedback submitted
  if (rating === UserFeedbackRating.ThumbDown && status === 'success') {
    return <FeedbackDetailsForm entityId={entityId!} entityType={entityType!} userId={userId!} setStatus={setStatus} />;
  }

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>
      <Container maxWidth="sm" sx={{ py: 10 }}>
        <Card sx={{ p: 5 }}>
          <Stack spacing={3} alignItems="center">
            <Typography variant="h4">Feedback</Typography>
            {status === 'success' && (
              <CustomAlert severity="success" title="Success">
                You have successfully submitted the feedback.
              </CustomAlert>
            )}
            {status === 'error' && (
              <CustomAlert severity="error" title="Error">
                {errorMessage}
              </CustomAlert>
            )}
          </Stack>
        </Card>
      </Container>
    </>
  );
}
