import { useEffect } from 'react';
import { useParams } from 'react-router';
import { useDispatch } from 'react-redux';
import { Helmet } from 'react-helmet-async';

import useFeatureFlags from 'src/hooks/feature-flags';

import { CONFIG } from 'src/global-config';
import { setLastViewedProjectId } from 'src/store/slices/settings/slice';

import TranscodingStatusMonitor from 'src/components/transcoding-status-monitor';

import ProjectDetailsView from 'src/sections/projects/view/details';
import ProjectDetailsViewV2 from 'src/sections/projects/view/details-v2';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Project Details` };

export default function ProjectDetailsPage() {
  const dispatch = useDispatch();
  const { id: projectId = '' } = useParams();
  const { hasNewLayout } = useFeatureFlags();

  useEffect(() => {
    dispatch(setLastViewedProjectId(projectId));
  }, [projectId]);

  return (
    <>
      <Helmet>
        <title> {metadata.title}</title>
      </Helmet>

      {hasNewLayout ? (
        <ProjectDetailsViewV2 projectId={projectId} />
      ) : (
        <ProjectDetailsView projectId={projectId} />
      )}

      <TranscodingStatusMonitor />
    </>
  );
}
