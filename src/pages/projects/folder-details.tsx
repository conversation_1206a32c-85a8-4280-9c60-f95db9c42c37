import { Helmet } from 'react-helmet-async';

import { CONFIG } from 'src/global-config';

import ProjectFolderDetailsView from 'src/sections/projects/view/folder-details';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Folder Details` };

export default function ProjectFolderDetailsPage() {
  return (
    <>
      <Helmet>
        <title> {metadata.title}</title>
      </Helmet>

      <ProjectFolderDetailsView />
    </>
  );
}
