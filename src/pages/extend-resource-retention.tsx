import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import Card from '@mui/material/Card';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import AlertTitle from '@mui/material/AlertTitle';

import { useSearchParams } from 'src/routes/hooks';

import { CONFIG } from 'src/global-config';
import { useExtendResourceRetentionMutation } from 'src/store/api/resources';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Extend Resource Retention` };

export default function ExtendResourceRetentionPage() {
  const searchParams = useSearchParams();

  // Get the invitation code and projectId from URL parameters
  const resourceId = searchParams.get('resourceId');

  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const [extendResourceRetention, { isLoading }] = useExtendResourceRetentionMutation();

  useEffect(() => {
    const handleExtendResourceRetention = async () => {
      // Check if required parameters are present
      if (!resourceId) {
        setStatus('error');
        setErrorMessage('Invalid resource link. Missing required parameters.');
        return;
      }

      try {
        // Call the API to accept the invitation
        await extendResourceRetention({
          id: resourceId,
        }).unwrap();

        setStatus('success');

      } catch (error) {
        setStatus('error');
        setErrorMessage(
          'Failed to extend resource retention. The resource may have expired or been deleted.'
        );
        console.error('Error extending resource retention:', error);
      }
    };

    handleExtendResourceRetention();
  }, [resourceId, extendResourceRetention]);

  if (status === 'loading') {
    return <LoadingScreen />;
  }

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <Container maxWidth="sm" sx={{ py: 10 }}>
        <Card sx={{ p: 5 }}>
          <Stack spacing={3} alignItems="center">
            <Typography variant="h4">Resource Retention</Typography>

            {status === 'success' && (
              <Alert severity="success">
                <AlertTitle>Success</AlertTitle>
                You have successfully extended the resource retention.
              </Alert>
            )}

            {status === 'error' && (
              <Alert severity="error">
                <AlertTitle>Error</AlertTitle>
                {errorMessage}
              </Alert>
            )}

          </Stack>
        </Card>
      </Container>
    </>
  );
}
