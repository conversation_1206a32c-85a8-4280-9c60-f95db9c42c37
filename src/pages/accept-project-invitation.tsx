/* eslint-disable react/no-unescaped-entities */
import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { useRouter, useSearchParams } from 'src/routes/hooks';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { CONFIG } from 'src/global-config';
import { useAcceptInvitationMutation } from 'src/store/api/projects';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Accept Project Invitation` };

export default function AcceptProjectInvitationPage() {
  useUserInitialContext();

  const router = useRouter();
  const searchParams = useSearchParams();

  // Get the invitation code and projectId from URL parameters
  const code = searchParams.get('code');
  const projectId = searchParams.get('projectId');

  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const [acceptInvitation] = useAcceptInvitationMutation();

  useEffect(() => {
    const handleAcceptInvitation = async () => {
      // Check if required parameters are present
      if (!code || !projectId) {
        setStatus('error');
        setErrorMessage('Invalid invitation link. Missing required parameters.');
        return;
      }

      try {
        // Call the API to accept the invitation
        await acceptInvitation({
          id: projectId,
          payload: {
            code,
          },
        }).unwrap();

        setStatus('success');

        // Redirect to project details page after a short delay
        setTimeout(() => {
          router.replace(paths.project.details(projectId));
        }, 1000);
        
      } catch (error) {
        setStatus('error');
        setErrorMessage(
          'Failed to accept invitation. The invitation may have expired or been revoked.'
        );
        console.error('Error accepting invitation:', error);
      }
    };

    handleAcceptInvitation();
  }, [code, projectId, acceptInvitation, router]);

  if (status === 'loading') {
    return <LoadingScreen />;
  }

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <Container maxWidth="sm" sx={{ py: 10 }}>
        <Card sx={{ p: 5 }}>
          <Stack spacing={3} alignItems="center">
            <Typography variant="h4">You have accepted the invitation to join the project</Typography>

            {status === 'success' && (
              <Card
                sx={(theme) => ({
                  width: '100%',
                  p: 3,
                  backgroundColor: theme.vars.palette.background.neutral,
                  border: `1px solid ${theme.vars.palette.divider}`,
                })}
              >
                <Typography variant="body2">
                  Redirecting you to the project page...
                </Typography>
              </Card>
            )}

            {status === 'error' && (
              <Card
                sx={(theme) => ({
                  width: '100%',
                  p: 3,
                  backgroundColor: theme.vars.palette.background.neutral,
                  border: `1px solid ${theme.vars.palette.divider}`,
                })}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                  Error
                </Typography>
                <Typography variant="body2">
                  {errorMessage}
                </Typography>
              </Card>
            )}
          </Stack>
        </Card>
      </Container>
    </>
  );
}
