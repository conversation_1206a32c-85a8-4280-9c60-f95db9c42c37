import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import { useDispatch } from 'react-redux';

import { addToUploadQueue } from 'src/store/slices/resources/slice';
import { useGetResourceUploadUrlMutation } from 'src/store/api/resources';

export const useUploadFile = () => {
  const dispatch = useDispatch();
  const [getUploadUrl] = useGetResourceUploadUrlMutation();

  const uploadFile = async (
    file: File,
    projectId?: string,
    folderId?: string,
    optional?: {
      transcoded?: boolean;
      mode: 'delete-original' | 'keep-both';
    }
  ) => {
    if (!file) return;

    try {
      const response = await getUploadUrl({
        payload: {
          fileName: file.name,
        },
      }).unwrap();

      dispatch(
        addToUploadQueue({
          id: uuidv4(),
          file,
          uploadUrlData: response,
          projectId,
          folderId,
          transcoded: optional?.transcoded,
          mode: optional?.mode,
        })
      );
    } catch (error: any) {
      toast.error('Something wrong! Unable to get upload url', {
        description: error.message,
      });
    }
  };

  return uploadFile;
};
