export const withHttps = (url: string) => (/^https?:\/\//i.test(url) ? url : `https://${url}`);

export const getUtmParams = () => {
  try {
    const searchParams = new URLSearchParams(window.location.search);
    const utmParams: Record<string, string> = {};

    // Standard UTM parameters
    const utmKeys = [
      'utm_source',
      'utm_medium',
      'utm_campaign',
      'utm_term',
      'utm_content',
      'utm_id', // For campaign ID
      'utm_creative', // For creative version
      'utm_placement', // For ad placement
      'utm_device', // For device targeting
      'utm_network', // For ad network
    ];

    utmKeys.forEach((key) => {
      const value = searchParams.get(key);
      if (value) {
        try {
          // Decode the URL-encoded value
          const decodedValue = decodeURIComponent(value);
          // Basic validation - remove any potentially harmful characters
          const sanitizedValue = decodedValue.replace(/[<>]/g, '');
          if (sanitizedValue) {
            utmParams[key] = sanitizedValue;
          }
        } catch (e) {
          console.warn(`Failed to decode UTM parameter ${key}:`, e);
        }
      }
    });

    return utmParams;
  } catch (e) {
    console.warn('Failed to get UTM parameters:', e);
    return {};
  }
};
