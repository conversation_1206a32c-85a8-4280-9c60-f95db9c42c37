import { projectsService } from './service';

export const {
  useGetProjectsQuery,
  useCreateProjectMutation,
  useDeleteProjectMutation,
  useLazyGetProjectDetailsQuery,
  useGetProjectDetailsQuery,
  useCreateProjectFolderMutation,
  useDeleteProjectFolderMutation,
  useGetProjectFolderDetailsQuery,
  useLazyGetListProjectMembersQuery,
  useLazyGetListPendingInvitationsQuery,
  useLazyGetListAccessRequestsQuery,
  useInviteUserMutation,
  useAcceptInvitationMutation,
  useResendInvitationMutation,
  useCancelInvitationMutation,
  useChangeRoleOfProjectMemberMutation,
  usePublishShareLinkMutation,
  useApproveOrRejectAccessMutation,
  useSharedLinkVerificationMutation,
  useGetProjectMembershipQuery,
  useRemoveProjectMemberMutation,
  useLeaveProjectMutation,
  useCreateProjectDefaultMutation,
  useUpdateProjectMutation,
} = projectsService;
