import { apiService } from '..';
import { calendarApiConfigs } from './configs';

import type { CalendarConfigParams } from './configs';

export const calendarService = apiService.injectEndpoints({
  endpoints: (build) => ({
    confirmCalendar: build.query<void, CalendarConfigParams<'confirmCalendar'>>({
      query: calendarApiConfigs.confirmCalendar,
      providesTags: ['Calendar'],
    }),
  }),
}); 