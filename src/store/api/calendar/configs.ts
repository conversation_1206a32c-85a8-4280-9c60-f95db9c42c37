import type { ApiRequestConfig } from 'src/store/api/types';

export interface CalendarApiConfigs {
  confirmCalendar: ApiRequestConfig<{
    fUid: string;
    eventId: string;
  }>;
}

export type CalendarConfigParams<Config extends keyof CalendarApiConfigs> = Parameters<
  CalendarApiConfigs[Config]
>[0];

export const calendarApiConfigs: CalendarApiConfigs = {
  confirmCalendar: (args) => ({
    method: 'GET',
    uri: 'confirm/aida',
    params: args,
  }),
}; 