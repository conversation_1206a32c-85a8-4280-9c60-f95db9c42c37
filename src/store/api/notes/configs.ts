import type { ApiRequestConfig } from 'src/store/api/types';

import type { NoteInput } from './types';

export interface NoteApiConfigs {
  getNotes: ApiRequestConfig<{}>;
  getNotesByResource: ApiRequestConfig<{
    resourceId: string;
  }>;
  getNotesByProject: ApiRequestConfig<{
    projectId: string;
  }>;
  getNote: ApiRequestConfig<{
    id: string;
  }>;
  createNote: ApiRequestConfig<{
    payload: NoteInput;
  }>;
  updateNote: ApiRequestConfig<{
    id: string;
    payload: Partial<NoteInput>;
  }>;
  deleteNote: ApiRequestConfig<{
    id: string;
  }>;
}

export type NoteConfigParams<Config extends keyof NoteApiConfigs> = Parameters<
  NoteApiConfigs[Config]
>[0];

export const noteApiConfigs: NoteApiConfigs = {
  getNotes: () => ({
    method: 'GET',
    uri: 'note',
  }),
  getNotesByResource: (args) => ({
    method: 'GET',
    uri: `notes?resourceId=${args.resourceId}`,
  }),
  getNotesByProject: (args) => ({
    method: 'GET',
    uri: `notes?projectId=${args.projectId}`,
  }),
  getNote: (args) => ({
    method: 'GET',
    uri: `note/${args.id}`,
  }),
  createNote: (args) => ({
    method: 'POST',
    uri: 'note',
    data: args.payload,
  }),
  updateNote: (args) => ({
    method: 'PUT',
    uri: `note/${args.id}`,
    data: args.payload,
  }),
  deleteNote: (args) => ({
    method: 'DELETE',
    uri: `note/${args.id}`,
  }),
};
