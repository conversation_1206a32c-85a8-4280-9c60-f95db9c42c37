import type { ApiRequestConfig } from 'src/store/api/types';

export enum UserFeedbackEntityType {
  Resource = 'resource',
  Project = 'project',
  Session = 'session',
  Summary = 'summary',
}

export enum UserFeedbackRating {
  ThumbUp = 'up',
  ThumbDown = 'down',
}

export interface UserFeedbackRequest {
  userId: string;
  rating: UserFeedbackRating;
  entityId: string;
  entityType: UserFeedbackEntityType;
}

export interface UserFeedbackResponse {
  id: string;
  userId: string;
  entityId: string;
  entityType: UserFeedbackEntityType;
  rating: UserFeedbackRating;
  createdAt: Date;
}

export interface UserFeedbackDetailsRequest {
  userId: string;
  entityId: string;
  entityType: UserFeedbackEntityType;
  rating: UserFeedbackRating;
  reason: string;
  comment: string;
}

export interface UserFeedbackApiConfigs {
  submitFeedback: ApiRequestConfig<{
    payload: UserFeedbackRequest;
  }>;
  submitFeedbackDetails: ApiRequestConfig<{
    payload: UserFeedbackDetailsRequest;
  }>;
}

export type UserFeedbackConfigParams<Config extends keyof UserFeedbackApiConfigs> = Parameters<
  UserFeedbackApiConfigs[Config]
>[0]; 