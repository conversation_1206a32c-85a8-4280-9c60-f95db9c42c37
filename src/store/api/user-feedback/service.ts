import { apiService } from '..';
import { userFeedbackConfigs } from './configs';

import type { UserFeedbackRating, UserFeedbackResponse, UserFeedbackEntityType } from './types';

export const userFeedbackService = apiService.injectEndpoints({
  endpoints: (build) => ({
    submitFeedback: build.mutation<UserFeedbackResponse, { payload: { userId: string; rating: UserFeedbackRating; entityId: string; entityType: UserFeedbackEntityType } }>({
      query: userFeedbackConfigs.submitFeedback,
      invalidatesTags: ['Resources'],
    }),
    submitFeedbackDetails: build.mutation<UserFeedbackResponse, { payload: { userId: string; entityId: string; entityType: UserFeedbackEntityType; rating: UserFeedbackRating; reason: string; comment: string } }>({
      query: userFeedbackConfigs.submitFeedbackDetails,
      invalidatesTags: ['Resources'],
    }),
  }),
  overrideExisting: true,
}); 