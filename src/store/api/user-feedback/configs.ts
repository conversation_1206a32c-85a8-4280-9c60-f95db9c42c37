import type { UserFeedbackApiConfigs, UserFeedbackConfigParams } from './types';

export const userFeedbackConfigs: UserFeedbackApiConfigs = {
  submitFeedback: (args: UserFeedbackConfigParams<'submitFeedback'>) => ({
    method: 'POST',
    uri: 'user-feedback/email',
    data: args.payload,
  }),
  submitFeedbackDetails: (args: UserFeedbackConfigParams<'submitFeedbackDetails'>) => ({
    method: 'POST',
    uri: 'user-feedback',
    data: args.payload,
  }),
}; 