import type { Method, AxiosRequestConfig } from 'axios';
import type { BaseQueryFn, FetchBaseQueryError } from '@reduxjs/toolkit/query';

export interface ApiRequestArgs
  extends Pick<
    AxiosRequestConfig,
    'data' | 'params' | 'responseType' | 'cancelToken' | 'onUploadProgress'
  > {
  uri: string;
  method: Method;
  headers?: AxiosRequestConfig['headers'];
}

export interface BaseQueryResult
  extends BaseQueryFn<ApiRequestArgs, unknown, FetchBaseQueryError> {}

type DefaultRequestArgs = {
  context?: any;
  /**
   * For query params
   */
  query?: any;
  /**
   * For body data
   */
  payload?: any;
  /**
   * For path params
   */
  [key: string]: any;
};

export type ApiRequestConfig<RequestArgs extends DefaultRequestArgs> = (
  args: RequestArgs
) => ApiRequestArgs;

export interface BaseResponse<T> {
  success: boolean;
  message: string;
  statusCode: number;
  data: T;
}
