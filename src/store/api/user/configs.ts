import type { ApiRequestConfig } from 'src/store/api/types';

export interface UserApiConfigs {
  getUserStatistics: ApiRequestConfig<{}>;
  getUserInitialContext: ApiRequestConfig<{}>;
}

export type UserConfigParams<Config extends keyof UserApiConfigs> = Parameters<
  UserApiConfigs[Config]
>[0];

export const userApiConfigs: UserApiConfigs = {
  getUserStatistics: () => ({
    method: 'GET',
    uri: 'user/statistics',
  }),
  getUserInitialContext: () => ({
    method: 'GET',
    uri: 'user/initial-context',
  }),
};
