import type { UserStatistics, UserInitialContext } from 'src/types';

import { apiService } from '..';
import { userApiConfigs } from './configs';

export const userService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getUserStatistics: build.query<UserStatistics, {}>({
      query: userApiConfigs.getUserStatistics,
    }),
    getUserInitialContext: build.query<UserInitialContext, {}>({
      query: userApiConfigs.getUserInitialContext,
      providesTags: ['InitialContext'],
    }),
  }),
  overrideExisting: true,
});
