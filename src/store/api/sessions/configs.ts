import type { ApiRequestConfig } from 'src/store/api/types';

import type { RecordSessionRequest, GetOngoingSessionsRequest } from './types';

export interface SessionApiConfigs {
  getOngoingSessions: ApiRequestConfig<GetOngoingSessionsRequest>;
  getSession: ApiRequestConfig<{ id: string }>;
  deleteSession: ApiRequestConfig<{ id: string }>;
  recordSession: ApiRequestConfig<{ payload: RecordSessionRequest }>;
  retrySession: ApiRequestConfig<{ id: string }>;
}

export type SessionConfigParams<Config extends keyof SessionApiConfigs> = Parameters<
  SessionApiConfigs[Config]
>[0];

export const sessionApiConfigs: SessionApiConfigs = {
  getOngoingSessions: (args) => ({
    method: 'GET',
    uri: 'sessions/ongoing',
    params: args.projectId ? { projectId: args.projectId } : {},
  }),
  getSession: (args) => ({
    method: 'GET',
    uri: `session/${args.id}`,
  }),
  deleteSession: (args) => ({
    method: 'DELETE',
    uri: `session/${args.id}`,
  }),
  recordSession: (args) => ({
    method: 'POST',
    uri: 'session/record-meeting',
    data: args.payload,
  }),
  retrySession: (args) => ({
    method: 'POST',
    uri: `session/retry/${args.id}`,
  }),
};
