import type { Session } from 'src/types';

import { apiService } from '..';
import { sessionApiConfigs } from './configs';
import { projectsService } from '../projects';
import { resourcesService } from '../resources';

import type { SessionConfigParams } from './configs';

export const sessionsService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getOngoingSessions: build.query<Session[], SessionConfigParams<'getOngoingSessions'>>({
      query: sessionApiConfigs.getOngoingSessions,
      async onQueryStarted(arg, { dispatch, queryFulfilled, getCacheEntry }) {
        const { data: cachedData = [] } = getCacheEntry();
        const { data } = await queryFulfilled;
        const hasCompletedSessions =
          cachedData.length > 0 &&
          cachedData.some(
            (cachedSession) => !data.find((session) => session.id === cachedSession.id)
          );

        // If any completed sessions are found, invalidate the resources cache
        if (hasCompletedSessions) {
          dispatch(resourcesService.util.invalidateTags(['Resources']));
          if (arg.projectId) {
            dispatch(
              projectsService.util.invalidateTags([{ type: 'Projects', id: arg.projectId }])
            );
          }
        }
      },
      providesTags: (result, error, arg) =>
        result
          ? [...result.map(({ id }) => ({ type: 'Sessions' as const, id })), 'Sessions']
          : ['Sessions'],
    }),
    getSession: build.query<Session, { id: string }>({
      query: sessionApiConfigs.getSession,
    }),
    deleteSession: build.mutation<Session, SessionConfigParams<'deleteSession'>>({
      query: sessionApiConfigs.deleteSession,
      async onQueryStarted(arg, { dispatch, queryFulfilled, getState }) {
        // Remove from ongoing sessions if present
        const state = getState() as any;
        const queries = state.api.queries;
        const patchResults: any[] = [];

        Object.keys(queries).forEach((queryKey) => {
          if (queryKey.startsWith('getOngoingSessions(')) {
            const query = queries[queryKey];
            if (query?.data?.some((session: any) => session.id === arg.id)) {
              // Extract projectId from query key if present
              const projectMatch = queryKey.match(/projectId":"([^"]+)"/);
              const projectId = projectMatch ? projectMatch[1] : undefined;

              const patch = dispatch(
                sessionsService.util.updateQueryData(
                  'getOngoingSessions',
                  projectId ? { projectId } : {},
                  (draft) => draft.filter((session) => session.id !== arg.id)
                )
              );
              patchResults.push(patch);
            }
          }
        });

        try {
          await queryFulfilled;
        } catch {
          // Revert all optimistic updates on error
          patchResults.forEach((patch) => patch.undo());
        }
      },
    }),
    recordSession: build.mutation<Session, SessionConfigParams<'recordSession'>>({
      query: sessionApiConfigs.recordSession,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;

          // Add to ongoing sessions if applicable
          if (arg.payload.projectId) {
            dispatch(
              sessionsService.util.updateQueryData(
                'getOngoingSessions',
                { projectId: arg.payload.projectId },
                (draft) => {
                  draft.unshift(data);
                }
              )
            );
          }

          // Also add to ongoing sessions without projectId filter
          dispatch(
            sessionsService.util.updateQueryData('getOngoingSessions', {}, (draft) => {
              draft.unshift(data);
            })
          );
        } catch {
          // Error handling - optimistic updates will be automatically reverted
        }
      },
    }),
    retrySession: build.mutation<Session, SessionConfigParams<'retrySession'>>({
      query: sessionApiConfigs.retrySession,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;

          // Update in ongoing sessions
          dispatch(
            sessionsService.util.updateQueryData('getOngoingSessions', {}, (draft) => {
              const index = draft.findIndex((session) => session.id === data.id);
              if (index !== -1) {
                draft[index] = data;
              }
            })
          );

          // Update individual session cache
          dispatch(sessionsService.util.updateQueryData('getSession', { id: data.id }, () => data));
        } catch {
          // Error handling - optimistic updates will be automatically reverted
        }
      },
    }),
  }),
  overrideExisting: true,
});
