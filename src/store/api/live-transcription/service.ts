import type { LiveTranscriptionJwtResponse } from 'src/types';

import { apiService } from '..';
import { liveTranscriptionApiConfigs } from './configs';

export const liveTranscriptionService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getLiveTranscriptionJwt: build.mutation<LiveTranscriptionJwtResponse, {}>({
      query: liveTranscriptionApiConfigs.getLiveTranscriptionJwt,
    }),
  }),
  overrideExisting: true,
});
