import type { ApiRequestConfig } from 'src/store/api/types';

export interface LiveTranscriptionApiConfigs {
  getLiveTranscriptionJwt: ApiRequestConfig<{}>;
}

export type LiveTranscriptionConfigParams<Config extends keyof LiveTranscriptionApiConfigs> =
  Parameters<LiveTranscriptionApiConfigs[Config]>[0];

export const liveTranscriptionApiConfigs: LiveTranscriptionApiConfigs = {
  getLiveTranscriptionJwt: () => ({
    method: 'POST',
    uri: 'live-transcribe/jwt',
  }),
};
