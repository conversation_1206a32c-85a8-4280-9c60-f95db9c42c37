import type { Draft } from 'immer';
import type { Resource } from 'src/types';
import type { ThunkDispatch } from '@reduxjs/toolkit';

import { resourcesService } from './service';
import { projectsService } from '../projects';

export const optimisticUpdateResourceForList = (
  resources: Draft<Resource[]>,
  updatedResource: Resource
) => {
  const index = resources.findIndex((item) => item.id === updatedResource.id);
  if (index === -1) return;
  resources[index] = { ...resources[index], ...updatedResource };
};

export const optimisticUpdateResourceForDetails = (
  resource: Draft<Resource>,
  updatedResource: Resource
) => ({
  ...resource,
  ...updatedResource,
});

export const optimisticAddResourceToList = (
  resources: Draft<Resource[]>,
  newResource: Resource
) => {
  // Add to the beginning of the list (most recent first)
  resources.unshift(newResource);
};

export const optimisticRemoveResourceFromList = (
  resources: Draft<Resource[]>,
  resourceId: string
) => {
  const index = resources.findIndex((item) => item.id === resourceId);
  if (index !== -1) {
    resources.splice(index, 1);
  }
};

export const optimisticUpdateResourceList = (
  dispatch: ThunkDispatch<any, any, any>,
  updatedResource: Resource
) => {
  dispatch(
    resourcesService.util.updateQueryData('getResources', {}, (draft) =>
      optimisticUpdateResourceForList(draft, updatedResource)
    )
  );
  dispatch(
    resourcesService.util.updateQueryData('getResource', { id: updatedResource.id }, (draft) =>
      optimisticUpdateResourceForDetails(draft, updatedResource)
    )
  );

  if (updatedResource.projectId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectDetails',
        { id: updatedResource.projectId },
        (draft) => optimisticUpdateResourceForList(draft.resources, updatedResource)
      )
    );
  }
};

export const optimisticAddResource = (
  dispatch: ThunkDispatch<any, any, any>,
  newResource: Resource
) => {
  // Update global resources list
  dispatch(
    resourcesService.util.updateQueryData('getResources', {}, (draft) =>
      optimisticAddResourceToList(draft, newResource)
    )
  );

  // Update project details if resource belongs to a project
  if (newResource.projectId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectDetails',
        { id: newResource.projectId },
        (draft) => optimisticAddResourceToList(draft.resources, newResource)
      )
    );
  }

  // Update project folder details if resource belongs to a folder
  if (newResource.projectId && newResource.folderId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectFolderDetails',
        { id: newResource.projectId, folderId: newResource.folderId },
        (draft) => optimisticAddResourceToList(draft.resources, newResource)
      )
    );
  }
};

export const optimisticRemoveResource = (
  dispatch: ThunkDispatch<any, any, any>,
  resourceId: string,
  projectId?: string,
  folderId?: string
) => {
  // Update global resources list
  dispatch(
    resourcesService.util.updateQueryData('getResources', {}, (draft) =>
      optimisticRemoveResourceFromList(draft, resourceId)
    )
  );

  // Update project details if resource belongs to a project
  if (projectId) {
    dispatch(
      projectsService.util.updateQueryData('getProjectDetails', { id: projectId }, (draft) =>
        optimisticRemoveResourceFromList(draft.resources, resourceId)
      )
    );
  }

  // Update project folder details if resource belongs to a folder
  if (projectId && folderId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectFolderDetails',
        { id: projectId, folderId },
        (draft) => optimisticRemoveResourceFromList(draft.resources, resourceId)
      )
    );
  }
};
