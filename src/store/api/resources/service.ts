import type { Resource } from 'src/types';

import { apiService } from '..';
import { projectsService } from '../projects';
import { resourceApiConfigs } from './configs';
import { optimisticAddResource, optimisticUpdateResourceList } from './utils';

import type { BaseResponse } from '../types';
import type { ResourceConfigParams } from './configs';
import type { TranscodingStatusItem, GetResourceUploadUrlResponse } from './types';

export const resourcesService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getResources: build.query<Resource[], {}>({
      query: resourceApiConfigs.getResources,
      transformResponse: (response: Resource[]) =>
        response.map((resource: Resource) => {
          const { transcription, ...rest } = resource;
          return {
            ...rest,
            transcription: transcription?.sort((a, b) => a.startTime - b.startTime),
          };
        }),
      providesTags: (result, error, arg) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'ResourceDetails' as const, id })),
              { type: 'Resources', id: 'LIST' },
              { type: 'Resources' },
            ]
          : ['Resources'],
    }),
    getResourceUploadUrl: build.mutation<
      GetResourceUploadUrlResponse,
      ResourceConfigParams<'getResourceUploadUrl'>
    >({
      query: resourceApiConfigs.getResourceUploadUrl,
    }),
    getResource: build.query<Resource, { id: string }>({
      query: resourceApiConfigs.getResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const { data } = await queryFulfilled;
        optimisticUpdateResourceList(dispatch, data);
      },
      transformResponse: (response: Resource) => ({
        ...response,
        transcription: response.transcription?.sort((a, b) => a.startTime - b.startTime),
      }),
      providesTags: (result) => [{ type: 'ResourceDetails', id: result?.id }],
    }),
    createResource: build.mutation<Resource, ResourceConfigParams<'createResource'>>({
      query: resourceApiConfigs.createResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          optimisticAddResource(dispatch, data);
        } catch {
          // Error handling - the optimistic update will be automatically reverted
          // since we're not doing any manual cache updates on error
        }
      },
      // Remove invalidatesTags to prevent unnecessary refetches
      // The optimistic update handles cache updates
    }),
    updateResource: build.mutation<Resource, ResourceConfigParams<'updateResource'>>({
      query: resourceApiConfigs.updateResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const { data } = await queryFulfilled;
        optimisticUpdateResourceList(dispatch, data);
      },
    }),
    deleteResource: build.mutation<Resource, ResourceConfigParams<'deleteResource'>>({
      query: resourceApiConfigs.deleteResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled, getState }) {
        // Perform optimistic removal from all relevant caches
        const patchResults = [];

        // Remove from global resources list
        const globalPatch = dispatch(
          resourcesService.util.updateQueryData('getResources', {}, (draft) =>
            draft.filter((item) => item.id !== arg.id)
          )
        );
        patchResults.push(globalPatch);

        // Update all project details caches that might contain this resource
        // We iterate through all cached project details and remove the resource
        const state = getState() as any;
        const projectDetailsQueries = state.api.queries;

        Object.keys(projectDetailsQueries).forEach((queryKey) => {
          if (queryKey.startsWith('getProjectDetails(')) {
            const query = projectDetailsQueries[queryKey];
            if (query?.data?.resources?.some((r: any) => r.id === arg.id)) {
              const projectId = query.originalArgs?.id;
              if (projectId) {
                const projectPatch = dispatch(
                  projectsService.util.updateQueryData(
                    'getProjectDetails',
                    { id: projectId },
                    (draft) => {
                      draft.resources = draft.resources.filter((item) => item.id !== arg.id);
                    }
                  )
                );
                patchResults.push(projectPatch);
              }
            }
          }
        });

        // Update all project folder details caches that might contain this resource
        Object.keys(projectDetailsQueries).forEach((queryKey) => {
          if (queryKey.startsWith('getProjectFolderDetails(')) {
            const query = projectDetailsQueries[queryKey];
            if (query?.data?.resources?.some((r: any) => r.id === arg.id)) {
              const { id: projectId, folderId } = query.originalArgs || {};
              if (projectId && folderId) {
                const folderPatch = dispatch(
                  projectsService.util.updateQueryData(
                    'getProjectFolderDetails',
                    { id: projectId, folderId },
                    (draft) => {
                      draft.resources = draft.resources.filter((item) => item.id !== arg.id);
                    }
                  )
                );
                patchResults.push(folderPatch);
              }
            }
          }
        });

        try {
          await queryFulfilled;
        } catch {
          // Revert all optimistic updates on error
          patchResults.forEach((patch) => patch.undo());
        }
      },
      // Remove invalidatesTags to prevent unnecessary refetches
      // The optimistic update handles cache updates
    }),
    uploadResourceThumbnail: build.mutation<
      { url: string },
      ResourceConfigParams<'uploadResourceThumbnail'>
    >({
      query: resourceApiConfigs.uploadResourceThumbnail,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const { data } = await queryFulfilled;

        dispatch(
          resourcesService.util.updateQueryData('getResources', {}, (draft) => {
            const index = draft.findIndex((item) => item.id === arg.id);
            if (index === -1) return;
            draft[index] = { ...draft[index], thumbnailUrl: data.url };
          })
        );
      },
    }),
    extendResourceRetention: build.mutation<
      Resource,
      ResourceConfigParams<'extendResourceRetention'>
    >({
      query: resourceApiConfigs.extendResourceRetention,
      invalidatesTags: (result) => {
        console.log('result', result);
        return ['Resources'];
      },
    }),
    transcodingStatus: build.query<
      TranscodingStatusItem[],
      ResourceConfigParams<'transcodingStatus'>
    >({
      query: resourceApiConfigs.transcodingStatus,
      transformResponse: (response: BaseResponse<TranscodingStatusItem[]>) => response.data,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          // Update the resources in the cache
          dispatch(
            projectsService.util.updateQueryData('getProjectDetails', { id: arg.id }, (draft) => {
              data.forEach((item) => {
                const resource = draft.resources.find(
                  (r) => r.id === item.id && r.isTranscoding !== item.isTranscoding
                );
                if (!resource) return;
                resource.isTranscoding = item.isTranscoding;
              });
            })
          );
        } catch (error) {
          console.log('error', error);
          // Error handling is managed by the polling hook
        }
      },
    }),
  }),
  overrideExisting: true,
});
