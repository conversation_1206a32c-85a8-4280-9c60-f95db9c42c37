import type { IApplicationState } from 'src/store';

import { createSelector } from '@reduxjs/toolkit';

export const selectFocusedNote = (state: IApplicationState) => state.notes.focusedNote;

export const selectSelectedNote = (state: IApplicationState) => state.notes.selectedNote;

export const selectHasSelectedNote = createSelector(
  [selectSelectedNote],
  (selectedNote) => selectedNote !== null
);

export const selectNotesLoading = (state: IApplicationState) => state.notes.isLoading;

export const selectNotesError = (state: IApplicationState) => state.notes.error;

export const selectNotes = (state: IApplicationState) => state.notes.notes;

export const selectNoteById = (id: string) =>
  createSelector([selectNotes], (notes) => notes.find((note) => note.id === id)); 