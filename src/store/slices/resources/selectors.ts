import type { IApplicationState } from 'src/store';

import { createSelector } from '@reduxjs/toolkit';

export const selectResourceUploadQueue = (state: IApplicationState) =>
  state.resources.resourceUploadQueue;

export const selectSortedResourceUploadQueue = createSelector(
  [selectResourceUploadQueue],
  (queue) =>
    [...queue].sort((a, b) => {
      if (!a.submittedAt || !b.submittedAt) return 0;
      const firstDate = new Date(a.submittedAt).getTime();
      const secondDate = new Date(b.submittedAt).getTime();

      return secondDate - firstDate;
    })
);

export const selectPendingResourceUploads = createSelector(
  [selectSortedResourceUploadQueue],
  (queue) =>
    queue.filter(
      (item) => item.status && ['pending', 'uploading', 'processing'].includes(item.status)
    )
);

export const selectPendingResourceUploadsForProject = createSelector(
  [selectPendingResourceUploads, (_, projectId: string) => projectId],
  (queue, projectId) => queue.filter((item) => item.projectId === projectId)
);

export const selectActiveResourceUploadsForProject = createSelector(
  [selectSortedResourceUploadQueue, (_, projectId: string) => projectId],
  (queue, projectId) =>
    queue.filter((item) => {
      if (item.projectId !== projectId) return false;

      // Show pending, uploading, and processing uploads
      if (item.status && ['pending', 'uploading', 'processing'].includes(item.status)) {
        return true;
      }

      return false;
    })
);

export const selectFocusedResource = (state: IApplicationState) => state.resources.focusedResource;

export const selectSelectedResources = (state: IApplicationState) =>
  state.resources.selectedResources;

export const selectSelectedResourceIds = createSelector(
  [selectSelectedResources],
  (selectedResources) => selectedResources.map((resource) => resource.id)
);
