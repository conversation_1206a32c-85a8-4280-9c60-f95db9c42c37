import type { IApplicationState } from 'src/store';

import { createSelector } from '@reduxjs/toolkit';

export const selectSettings = createSelector(
  (state: IApplicationState) => state.settings,
  (settings) => settings
);

export const selectTableDense = createSelector(selectSettings, (settings) => settings.tableDense);

export const selectLastViewedProjectId = createSelector(
  selectSettings,
  (settings) => settings.lastViewedProjectId
);
