import type { PayloadAction } from '@reduxjs/toolkit';

import { createSlice } from '@reduxjs/toolkit';

export type SettingsSliceState = {
  tableDense: boolean;
  lastViewedProjectId: string | null;
};

export const settingsInitialState: SettingsSliceState = {
  tableDense: false,
  lastViewedProjectId: null,
};

export const settingsSlice = createSlice({
  name: 'settings',
  initialState: settingsInitialState,
  reducers: {
    setTableDense: (state, action: PayloadAction<boolean>) => {
      state.tableDense = action.payload;
    },
    setLastViewedProjectId: (state, action: PayloadAction<string | null>) => {
      state.lastViewedProjectId = action.payload;
    },
  },
});

export const { setTableDense, setLastViewedProjectId } = settingsSlice.actions;

export default settingsSlice.reducer;
