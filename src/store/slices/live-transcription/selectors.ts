import type { IApplicationState } from 'src/store';

import { createSelector } from '@reduxjs/toolkit';

export const selectSelectedDevice = (state: IApplicationState) => state.liveTranscription.selectedDevice;

export const selectIsLiveTranscription = (state: IApplicationState) => state.liveTranscription.isLiveTranscription;

export const selectHasSelectedDevice = createSelector(
  [selectSelectedDevice],
  (selectedDevice) => selectedDevice !== null
); 