import type { PayloadAction } from '@reduxjs/toolkit';

import { createSlice } from '@reduxjs/toolkit';

interface LiveTranscribeState {
  selectedDevice: string | null;
  isLiveTranscription: boolean;
}

const initialState: LiveTranscribeState = {
  selectedDevice: null,
  isLiveTranscription: false,
};

const liveTranscribeSlice = createSlice({
  name: 'liveTranscribe',
  initialState,
  reducers: {
    setSelectedDevice: (state, action: PayloadAction<string | null>) => {
      state.selectedDevice = action.payload;
    },
    setIsLiveTranscription: (state, action: PayloadAction<boolean>) => {
      state.isLiveTranscription = action.payload;
    },
  },
});

export const { setSelectedDevice, setIsLiveTranscription } = liveTranscribeSlice.actions;

export default liveTranscribeSlice.reducer; 