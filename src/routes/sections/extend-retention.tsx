import type { RouteObject } from 'react-router';

import { lazy, Suspense } from 'react';

import { paths } from 'src/routes/paths';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

const ExtendResourceRetentionPage = lazy(() => import('src/pages/extend-resource-retention'));

// ----------------------------------------------------------------------

export const extendRetentionRoutes: RouteObject[] = [
  {
    path: paths.resource.extendRetention,
    element: (
      <AuthGuard>
        <Suspense fallback={<LoadingScreen />}>
          <ExtendResourceRetentionPage />
        </Suspense>
      </AuthGuard>
    ),
  },
];
