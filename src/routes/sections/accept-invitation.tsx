import type { RouteObject } from 'react-router';

import { lazy, Suspense } from 'react';

import { paths } from 'src/routes/paths';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

const AcceptProjectInvitationPage = lazy(() => import('src/pages/accept-project-invitation'));

// ----------------------------------------------------------------------

export const acceptInvitationRoutes: RouteObject[] = [
  {
    path: paths.project.acceptInvitation,
    element: (
      <AuthGuard>
        <Suspense fallback={<LoadingScreen />}>
          <AcceptProjectInvitationPage />
        </Suspense>
      </AuthGuard>
    ),
  },
];
