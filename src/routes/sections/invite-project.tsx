import type { RouteObject } from 'react-router';

import { lazy, Suspense } from 'react';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

const VerifyProjectSharedLinkPage = lazy(() => import('src/pages/verify-project-shared-link'));

// ----------------------------------------------------------------------

export const inviteProjectRoutes: RouteObject[] = [
  {
    path: 'invite-project/:projectId',
    element: (
      <AuthGuard>
        <Suspense fallback={<LoadingScreen />}>
          <VerifyProjectSharedLinkPage />
        </Suspense>
      </AuthGuard>
    ),
  },
];
