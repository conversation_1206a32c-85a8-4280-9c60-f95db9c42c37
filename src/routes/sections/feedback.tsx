import type { RouteObject } from 'react-router';

import { lazy, Suspense } from 'react';

import { paths } from 'src/routes/paths';

import { LoadingScreen } from 'src/components/loading-screen';


// ----------------------------------------------------------------------

const FeedbackPage = lazy(() => import('src/pages/feedback'));

// ----------------------------------------------------------------------

export const feedbackRoutes: RouteObject[] = [
  {
    path: paths.feedback.root,
    element: (
        <Suspense fallback={<LoadingScreen />}>
          <FeedbackPage />
        </Suspense>
    ),
  },
];
