import type { RouteObject } from 'react-router';

import { Outlet } from 'react-router';
import { lazy, Suspense } from 'react';

import { AuthSplitLayout } from 'src/layouts/auth-split';

import { SplashScreen } from 'src/components/loading-screen';

import { GuestGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

/** **************************************
 * Firebase
 *************************************** */
const Auth = {
  SignInPage: lazy(() => import('src/pages/auth/sign-in')),
  SignUpPage: lazy(() => import('src/pages/auth/sign-up')),
  VerifyPage: lazy(() => import('src/pages/auth/verify')),
  ResetPasswordPage: lazy(() => import('src/pages/auth/reset-password')),
};

// ----------------------------------------------------------------------

export const authRoutes: RouteObject[] = [
  {
    path: 'auth',
    element: (
      <Suspense fallback={<SplashScreen />}>
        <Outlet />
      </Suspense>
    ),
    children: [
      {
        path: 'sign-in',
        element: (
          <GuestGuard>
            <AuthSplitLayout
              slotProps={{
                section: { title: 'Hi, Welcome back' },
              }}
            >
              <Auth.SignInPage />
            </AuthSplitLayout>
          </GuestGuard>
        ),
      },
      {
        path: 'sign-up',
        element: (
          <GuestGuard>
            <AuthSplitLayout>
              <Auth.SignUpPage />
            </AuthSplitLayout>
          </GuestGuard>
        ),
      },
      {
        path: 'verify',
        element: (
          <AuthSplitLayout>
            <Auth.VerifyPage />
          </AuthSplitLayout>
        ),
      },
      {
        path: 'reset-password',
        element: (
          <AuthSplitLayout>
            <Auth.ResetPasswordPage />
          </AuthSplitLayout>
        ),
      },
    ],
  },
];
