import type { RouteObject } from 'react-router';

import { lazy } from 'react';

import { paths } from 'src/routes/paths';

import { AuthSplitLayout } from 'src/layouts/auth-split';

import { GuestGuard } from 'src/auth/guard';


// ----------------------------------------------------------------------

/** **************************************
 * Firebase
 *************************************** */
const ConfirmMeeting = {
  ConfirmMeetingPage: lazy(() => import('src/pages/auth/confirm-meeting')),
};

// ----------------------------------------------------------------------

export const confirmMeetingRoute: RouteObject[] = [
  {
    path: paths.confirmMeeting.root,
    element: (
      <GuestGuard>
        <AuthSplitLayout>
          <ConfirmMeeting.ConfirmMeetingPage />
        </AuthSplitLayout>
      </GuestGuard>
    ),
  },
];
