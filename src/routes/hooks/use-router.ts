import { useMemo } from 'react';
import { useNavigate } from 'react-router';

import { getUtmParams } from 'src/utils/url';

// ----------------------------------------------------------------------

/**
 * Handles URL navigation while preserving UTM parameters and existing query parameters
 * @param href - The target URL to navigate to
 * @param options - Navigation options (e.g., replace: true)
 * @returns The processed URL with preserved parameters
 */
const processNavigationUrl = (href: string, options?: { replace?: boolean }) => {
  const utmParams = getUtmParams();
  const url = new URL(href, window.location.origin);

  // Preserve existing query parameters
  const existingParams = new URLSearchParams(url.search);

  // Add UTM parameters only if they don't already exist
  Object.entries(utmParams).forEach(([key, value]) => {
    if (!existingParams.has(key)) {
      existingParams.set(key, value);
    }
  });

  return {
    pathname: url.pathname,
    search: existingParams.toString() ? `?${existingParams.toString()}` : '',
    options,
  };
};

/**
 * Custom router hook that extends React Router's navigation capabilities
 * with UTM parameter preservation.
 *
 * @example
 * // Basic navigation with UTM preservation
 * router.push('/dashboard');
 * // If current URL has ?utm_source=google, navigates to /dashboard?utm_source=google
 *
 * @example
 * // Navigation with existing query params
 * router.push('/dashboard?view=compact');
 * // If current URL has ?utm_source=google, navigates to /dashboard?view=compact&utm_source=google
 *
 * @example
 * // Navigation with conflicting UTM params
 * router.push('/dashboard?utm_source=facebook');
 * // Navigates to /dashboard?utm_source=facebook (preserves the new value)
 *
 * @example
 * // Replace current history entry
 * router.replace('/profile');
 * // Replaces current URL while preserving UTM params
 */
export function useRouter() {
  const navigate = useNavigate();

  const router = useMemo(
    () => ({
      back: () => navigate(-1),
      forward: () => navigate(1),
      refresh: () => navigate(0),
      push: (href: string) => {
        const { pathname, search } = processNavigationUrl(href);
        navigate(pathname + search);
      },
      replace: (href: string) => {
        const { pathname, search } = processNavigationUrl(href, { replace: true });
        navigate(pathname + search, { replace: true });
      },
    }),
    [navigate]
  );

  return router;
}
