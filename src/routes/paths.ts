// ----------------------------------------------------------------------

const ROOTS = {
  AUTH: '/auth',
  FILES: '/files',
  PROJECT: '/project',
  CONFIRM_MEETING: '/confirm-aida',
  INVITE_PROJECT: '/invite-project',
  ACCEPT_PROJECT_INVITATION: '/accept-project-invitation',
  EXTEND_RESOURCE_RETENTION: '/extend-resource-retention',
  FEEDBACK: '/feedback',
};

// ----------------------------------------------------------------------

export const paths = {
  faqs: '/faqs',
  // AUTH
  auth: {
    signIn: `${ROOTS.AUTH}/sign-in`,
    verify: `${ROOTS.AUTH}/verify`,
    signUp: `${ROOTS.AUTH}/sign-up`,
    resetPassword: `${ROOTS.AUTH}/reset-password`,
  },
  files: {
    root: ROOTS.FILES,
  },
  project: {
    root: ROOTS.PROJECT,
    details: (id: string) => `${ROOTS.PROJECT}/${id}`,
    folder: (id: string, folderId: string) => `${ROOTS.PROJECT}/${id}/folder/${folderId}`,
    acceptInvitation: `${ROOTS.ACCEPT_PROJECT_INVITATION}`,
    inviteProject: `${ROOTS.INVITE_PROJECT}/:projectId`,
  },
  confirmMeeting: {
    root: ROOTS.CONFIRM_MEETING,
  },
  inviteProject: {
    root: ROOTS.INVITE_PROJECT,
    acceptInvitation: `${ROOTS.ACCEPT_PROJECT_INVITATION}`,
  },
  resource: {
    root: ROOTS.EXTEND_RESOURCE_RETENTION,
    extendRetention: `${ROOTS.EXTEND_RESOURCE_RETENTION}`,
  },
  feedback: {
    root: ROOTS.FEEDBACK,
  },
};
