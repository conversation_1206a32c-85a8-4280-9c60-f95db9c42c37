import 'src/global.css';

import { useEffect } from 'react';
import { Provider, ReactReduxContext } from 'react-redux';

import { usePathname } from 'src/routes/hooks';

import { themeConfig, ThemeProvider } from 'src/theme';

import { ProgressBar } from 'src/components/progress-bar';
import { MotionLazy } from 'src/components/animate/motion-lazy';
import { SettingsDrawer, defaultSettings, SettingsProvider } from 'src/components/settings';

import { InviteDialog, InviteProvider } from 'src/sections/projects/components/invite-dialog';

import { AuthProvider } from 'src/auth/context/firebase';

import { Snackbar } from './components/snackbar';
import ResourceDialog from './sections/resources/components/resource-dialog';
import BackgroundResourceUploader from './sections/resources/components/background-resource-uploader';

import type { AppStore } from './store';

// ----------------------------------------------------------------------

type AppProps = {
  store: AppStore;
  children: React.ReactNode;
};

export default function App({ children, store }: AppProps) {
  useScrollToTop();

  return (
    <Provider store={store} context={ReactReduxContext}>
      <AuthProvider>
        <SettingsProvider defaultSettings={defaultSettings}>
          <ThemeProvider
            noSsr
            defaultMode={themeConfig.defaultMode}
            modeStorageKey={themeConfig.modeStorageKey}
          >
            <InviteProvider>
              <MotionLazy>
                <Snackbar />
                <ProgressBar />
                <SettingsDrawer defaultSettings={defaultSettings} />
                {/* Invite dialog on Project Details page */}
                <InviteDialog />
                {/* Background resource uploader */}
                <BackgroundResourceUploader />
                {/* Dialog for focused resource details */}
                <ResourceDialog />
                {children}
              </MotionLazy>
            </InviteProvider>
          </ThemeProvider>
        </SettingsProvider>
      </AuthProvider>
    </Provider>
  );
}

// ----------------------------------------------------------------------

function useScrollToTop() {
  const pathname = usePathname();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}
