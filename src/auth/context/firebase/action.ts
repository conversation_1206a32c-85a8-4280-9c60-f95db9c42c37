import type { UserCredential } from 'firebase/auth';

import { doc, setDoc, collection } from 'firebase/firestore';
import {
  signOut as _signOut,
  signInWithPopup as _signInWithPopup,
  GoogleAuthProvider as _GoogleAuthProvider,
  OAuthProvider as _OAuthProvider,
  sendEmailVerification as _sendEmailVerification,
  sendPasswordResetEmail as _sendPasswordResetEmail,
  signInWithEmailAndPassword as _signInWithEmailAndPassword,
  createUserWithEmailAndPassword as _createUserWithEmailAndPassword,
} from 'firebase/auth';

import { AUTH, FIRESTORE } from 'src/lib/firebase';

// ----------------------------------------------------------------------

export type SignInParams = {
  email: string;
  password: string;
};

export type SignUpParams = {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
};

export type ForgotPasswordParams = {
  email: string;
};

/** **************************************
 * Sign in
 *************************************** */
export const signInWithPassword = async ({ email, password }: SignInParams): Promise<void> => {
  try {
    await _signInWithEmailAndPassword(AUTH, email, password);

    const user = AUTH.currentUser;

    if (!user?.emailVerified) {
      throw new Error('Email not verified!');
    }
  } catch (error) {
    console.error('Error during sign in with password:', error);
    throw error;
  }
};

export const signInWithGoogle = async (): Promise<UserCredential> => {
  const provider = new _GoogleAuthProvider();
  return await _signInWithPopup(AUTH, provider);
};

export const signInWithMicrosoft = async (): Promise<UserCredential> => {
  const provider = new _OAuthProvider('microsoft.com');
  return await _signInWithPopup(AUTH, provider);
};


/** **************************************
 * Sign up
 *************************************** */
export const signUp = async ({
  email,
  password,
  firstName,
  lastName,
}: SignUpParams): Promise<UserCredential> => {
  try {
    const newUser = await _createUserWithEmailAndPassword(AUTH, email, password);

    /*
     * (1) If skip emailVerified
     * Remove : await _sendEmailVerification(newUser.user);
     */
    await _sendEmailVerification(newUser.user);

    const userProfile = doc(collection(FIRESTORE, 'users'), newUser.user?.uid);

    await setDoc(userProfile, {
      uid: newUser.user?.uid,
      email,
      displayName: `${firstName} ${lastName}`,
      preferences: {
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      }
    });

    return newUser;
  } catch (error) {
    console.error('Error during sign up:', error);
    throw error;
  }
};

export const insertSSOUserToFirestore = async (userCredential: UserCredential): Promise<void> => {
  try {
    const { user } = userCredential;
    const userProfile = doc(collection(FIRESTORE, 'users'), user.uid);

    await setDoc(userProfile, {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      preferences: {
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      }
    });

  } catch (error) {
    console.error('Error during sign up with SSO:', error);
    throw error;
  }
}

/** **************************************
 * Sign out
 *************************************** */
export const signOut = async (): Promise<void> => {
  await _signOut(AUTH);
};

/** **************************************
 * Reset password
 *************************************** */
export const sendPasswordResetEmail = async ({ email }: ForgotPasswordParams): Promise<void> => {
  await _sendPasswordResetEmail(AUTH, email);
};
