import { useMemo } from 'react';
import { useSearchParams } from 'react-router';

import { EmailInboxIcon } from 'src/assets/icons';
import { useConfirmCalendarQuery } from 'src/store/api/calendar';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

export function ConfirmMeetingView() {
  const [searchParams] = useSearchParams();
  const uid = useMemo(() => searchParams.get('uid'), [searchParams]);
  const eventId = useMemo(() => searchParams.get('eventId'), [searchParams]);
  const { isLoading, error } = useConfirmCalendarQuery({
    fUid: uid ?? '',
    eventId: eventId ?? '',
  });

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 text-center">
        <div className="max-w-md space-y-4">
          <h2 className="text-2xl font-bold text-gray-900">Oops!</h2>
          <div className="rounded-lg bg-red-50 p-4 text-red-700">
            <p>
              {!uid || !eventId
                ? 'Your code is missing, please check the link and try again.'
                : 'There was an error confirming your magic link, please check the link and try again.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 text-center">
      <div className="max-w-md space-y-6">
        <div className="mx-auto h-16 w-16 rounded-full bg-green-100 p-3">
          <EmailInboxIcon className="h-full w-full text-green-600" />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900">Meeting Confirmed!</h2>
        
        <div className="space-y-4 rounded-lg bg-gray-50 p-6 text-left">
          <p className="text-gray-600">
            Your meeting has been successfully confirmed. Aida will join as a guest and record the session as planned.
          </p>
          
          <div className="space-y-2">
            <h3 className="font-semibold text-gray-900">What happens next:</h3>
            <ul className="list-inside list-disc space-y-2 text-gray-600">
              <li>Aida will automatically join your meeting at the scheduled time</li>
              <li>The session will be recorded and transcribed in real-time</li>
              <li>After the meeting, you&apos;ll receive a summary email with highlights and action items</li>
              <li>Access the full recording and transcript anytime on the Beings platform</li>
            </ul>
          </div>
          
          <p className="text-sm text-gray-500">
            You can close this window now. you&apos;ll see you in the meeting!
          </p>
        </div>
      </div>
    </div>
  );
}
