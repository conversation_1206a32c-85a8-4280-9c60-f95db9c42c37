import type { AppFeatures } from 'src/types';

import useFeatureFlags from 'src/hooks/feature-flags';

import { SplashScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

type FeatureFlagGuardProps = {
  children: React.ReactNode;
  featureFlag: AppFeatures;
};

export function FeatureFlagGuard({ children, featureFlag }: FeatureFlagGuardProps) {
  const { isFlagEnabled } = useFeatureFlags();

  if (!isFlagEnabled(featureFlag)) {
    return <SplashScreen />;
  }

  return <>{children}</>;
}
