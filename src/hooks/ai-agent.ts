import type { ProjectDetails } from 'src/types/project';
import type { Resource, IChatMessage, Transcription } from 'src/types';
import type { Part, ChatSession, UsageMetadata, GenerativeModel } from 'firebase/vertexai';

import { toast } from 'sonner';
import { useState } from 'react';
import * as Sentry from '@sentry/react';
import { uuidv4 } from 'minimal-shared/utils';
import { getGenerativeModel } from 'firebase/vertexai';

import { fSub, fSecsToTime } from 'src/utils/format-time';

import { CONFIG } from 'src/global-config';
import { AUTH, VERTEXAI } from 'src/lib/firebase';

import { fileFormat } from 'src/components/file-thumbnail';

import useAnalytics from './analytics';

const SYSTEM_INSTRUCTIONS: Part[] = [
  {
    text: 'You are a helpful AI assistant focused on analyzing files and their content. Be concise and direct in responses while maintaining accuracy.',
  },
  {
    text: 'Context format:\n- Files: "name" (ID: id)\n- Folders: "name" (ID: id)\n- Projects: "name" (ID: id)\n- Transcriptions: <speaker>: <startTime>-<endTime>: <content>',
  },
  {
    text: 'Guidelines:\n1. Use provided context to answer questions\n2. Never mention IDs in responses\n3. Prioritize relevant information from transcriptions\n4. Keep responses focused on the content being discussed\n5. Process initial context once and use it for all subsequent questions',
  },
  {
    text: 'Response style:\n- Be direct and to the point\n- Use bullet points for lists\n- Format timestamps as HH:MM:SS\n- Reference specific content when relevant\n- No need to reprocess or repeat context in responses',
  },
];

const MAX_MESSAGE_CHUNK_SIZE = 1000;

const useAiAgent = () => {
  const { trackEvent } = useAnalytics();

  const [model, setModel] = useState<GenerativeModel | null>(null);
  const [session, setSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<IChatMessage[]>([]);
  const [isReplying, setIsReplying] = useState(false);
  const [isStartingConversation, setIsStartingConversation] = useState(false);
  const [pendingInstructions, setPendingInstructions] = useState<Part[]>([]);
  const [activeResources, setActiveResources] = useState<Resource[]>([]);

  const formatTranscriptionText = (transcription: Transcription) =>
    `${transcription.nameFromRevAi}: ${fSecsToTime(transcription.startTime)}-${fSecsToTime(transcription.endTime)}: ${transcription.content}`;

  const convertTranscriptionsToTextFile = (transcriptions: Transcription[]): Part[] => {
    if (!transcriptions?.length) return [];

    const textContent = transcriptions.map((t) => formatTranscriptionText(t)).join('\n\n');

    // Handle potential encoding issues with non-ASCII characters
    const encodedContent = btoa(unescape(encodeURIComponent(textContent)));

    // If content is too large, we'll split it into chunks
    const MAX_CHUNK_SIZE = 1000000; // ~1MB base64 encoded
    if (encodedContent.length > MAX_CHUNK_SIZE) {
      // Split into chunks and send as multiple parts
      const chunks = [];
      for (let i = 0; i < encodedContent.length; i += MAX_CHUNK_SIZE) {
        chunks.push(encodedContent.slice(i, i + MAX_CHUNK_SIZE));
      }

      return [
        {
          text: 'Transcription content (split into multiple parts):',
        },
        ...chunks.map((chunk) => ({
          inlineData: {
            data: chunk,
            mimeType: 'text/plain',
          },
        })),
      ];
    }

    return [
      {
        text: 'Transcription content:',
      },
      {
        inlineData: {
          data: encodedContent,
          mimeType: 'text/plain',
        },
      },
    ];
  };

  const formatFileData = (resource: Resource): Part[] => {
    const format = fileFormat(resource.fileName || resource.name || '');
    const isText = format === 'txt';
    const isPdf = format === 'pdf';

    if (!isText && !isPdf) {
      return [];
    }

    return [
      {
        text: `Content for file "${resource.name}" (ID: ${resource.id}):`,
      },
      {
        fileData: {
          fileUri: resource.url,
          mimeType: isText ? 'text/plain' : 'application/pdf',
        },
      },
    ];
  };

  const formatInstructionForResource = (resource: Resource) => [
    {
      text: `File "${resource.name}" (ID: ${resource.id})`,
    },
    ...(resource.projectId ? [{ text: `Project (ID: ${resource.projectId})` }] : []),
    ...(resource.folderId ? [{ text: `Folder (ID: ${resource.folderId})` }] : []),
    ...(resource.transcription ? convertTranscriptionsToTextFile(resource.transcription) : []),
    ...formatFileData(resource),
  ];

  const appendMessage = (message: string, sender: 'user' | 'bot', existingMessageId?: string) => {
    const userId = AUTH.currentUser?.uid ?? 'user';

    const messageId = existingMessageId ?? uuidv4();

    if (existingMessageId) {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === existingMessageId ? { ...msg, body: msg.body + message } : msg
        )
      );
    } else {
      const messageData: IChatMessage = {
        id: messageId,
        body: message,
        createdAt: fSub({ minutes: 1 }),
        senderId: sender === 'user' ? userId : 'bot',
      };
      setMessages((prev) => [...prev, messageData]);
    }

    return messageId;
  };

  const startConversation = async (instructions: Part[] = []) => {
    if (session || model) {
      return undefined;
    }
    try {
      setIsStartingConversation(true);

      // Init model
      const newModel = getGenerativeModel(
        VERTEXAI,
        {
          model: CONFIG.firebase.geminiModel,
          systemInstruction: {
            role: 'system',
            parts: SYSTEM_INSTRUCTIONS,
          },
        },
        {
          timeout: 600 * 1000, // 10 minutes
        }
      );
      setModel(newModel);

      // Init session
      const newSession = newModel.startChat();
      setSession(newSession);

      // Store instructions for later use
      setPendingInstructions(instructions);

      // Track event
      trackEvent({
        eventCategory: 'Aida Chat',
        eventAction: 'Started conversation',
        properties: {
          instructions,
        },
      });

      // Append first message from bot
      appendMessage('How can I assist you today?', 'bot');

      return newSession;
    } catch (error) {
      toast.error('Failed to start conversation', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
      Sentry.captureException(error);
      return undefined;
    } finally {
      setIsStartingConversation(false);
    }
  };

  const processPendingInstructions = async () => {
    if (!session || pendingInstructions.length === 0) return;

    // Process instructions in chunks for better performance
    const chunks = [];
    for (let i = 0; i < pendingInstructions.length; i += MAX_MESSAGE_CHUNK_SIZE) {
      chunks.push(pendingInstructions.slice(i, i + MAX_MESSAGE_CHUNK_SIZE));
    }

    // Process each chunk with clear context markers
    for (const chunk of chunks) {
      await session.sendMessage([
        ...chunk,
        {
          text: 'This is context information. Process it once and use it for all subsequent questions. No need to acknowledge or repeat this context in responses.',
        },
      ]);
    }

    // Clear pending instructions
    setPendingInstructions([]);
  };

  const sendMessage = async (message: string) => {
    if (!session) {
      toast.error('Please start a conversation first');
      return;
    }

    try {
      // Append user message
      appendMessage(message, 'user');
      setIsReplying(true);

      // If this is the first message and we have pending instructions, process them first
      if (pendingInstructions.length > 0) {
        await processPendingInstructions();
      }

      // Trigger send message
      const result = await session.sendMessageStream(message);

      // Prepare an empty bot message
      const botMessageId = appendMessage('', 'bot');
      let usage: UsageMetadata | undefined;

      // Accumulate text and update the message as chunks arrive
      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        usage = chunk.usageMetadata;

        appendMessage(chunkText, 'bot', botMessageId);
      }

      trackEvent({
        eventCategory: 'Aida Chat',
        eventAction: 'Sent message',
        properties: {
          message,
          usage,
        },
      });
    } catch (error) {
      toast.error('Failed to send message', {
        description: 'Something went wrong. Please try again later.',
      });
      Sentry.captureException(error);
    } finally {
      setIsReplying(false);
    }
  };

  const updateContextWithNewResources = async (newResources: Resource[]) => {
    if (!session || !newResources.length) return;

    // Filter out resources that are already in the context
    const uniqueNewResources = newResources.filter(
      (newRes) => !activeResources.some((existingRes) => existingRes.id === newRes.id)
    );

    if (!uniqueNewResources.length) return;

    try {
      // Update active resources
      setActiveResources((prev) => [...prev, ...uniqueNewResources]);

      // Add to pending instructions for next message
      const newInstructions: Part[] = [
        {
          text: `Adding ${uniqueNewResources.length} new files to context:\n${uniqueNewResources
            .map((r) => `- "${r.name}" (ID: ${r.id})`)
            .join('\n')}`,
        },
      ];

      uniqueNewResources.forEach((r) => {
        const instruction = formatInstructionForResource(r);
        newInstructions.push(...instruction);
      });

      newInstructions.push({
        text: 'These are additional context files. Process them and use them for all subsequent questions. No need to acknowledge or repeat this context in responses.',
      });

      setPendingInstructions((prev) => [...prev, ...newInstructions]);

      trackEvent({
        eventCategory: 'Aida Chat',
        eventAction: 'Updated context',
        properties: {
          newResources: uniqueNewResources.map((r) => r.id),
        },
      });
    } catch (error) {
      toast.error('Failed to update context', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
      Sentry.captureException(error);
    }
  };

  const removeResourcesFromContext = async (resourcesToRemove: Resource[]) => {
    if (!session || !resourcesToRemove.length) return;

    try {
      // Update active resources by removing the specified resources
      setActiveResources((prev) =>
        prev.filter((res) => !resourcesToRemove.some((r) => r.id === res.id))
      );

      // Add to pending instructions for next message
      const removeInstructions: Part[] = [
        {
          text: `Removing ${resourcesToRemove.length} files from context:\n${resourcesToRemove
            .map((r) => `- "${r.name}" (ID: ${r.id})`)
            .join('\n')}`,
        },
        {
          text: 'These files are no longer available for reference. Please do not use them in future responses.',
        },
      ];

      setPendingInstructions((prev) => [...prev, ...removeInstructions]);

      trackEvent({
        eventCategory: 'Aida Chat',
        eventAction: 'Removed from context',
        properties: {
          removedResources: resourcesToRemove.map((r) => r.id),
        },
      });
    } catch (error) {
      toast.error('Failed to remove files from context', {
        description: error instanceof Error ? error.message : 'Unknown error',
      });
      Sentry.captureException(error);
    }
  };

  /**
   * Start a conversation for multiple resources
   * @param resources - The resources to start a conversation for
   * @returns void
   */
  const startConversationWithResources = async (resources: Resource[]) => {
    // Allow starting a conversation with no resources
    if (resources.length === 0) {
      await startConversation([]);
      return;
    }

    const instructions: Part[] = [
      {
        text: `Analyzing ${resources.length} files:\n${resources.map((r) => `- "${r.name}" (ID: ${r.id})`).join('\n')}`,
      },
    ];

    resources.forEach((r) => {
      const instruction = formatInstructionForResource(r);
      instructions.push(...instruction);
    });

    // Set initial active resources
    setActiveResources(resources);
    await startConversation(instructions);
  };

  /**
   * Start a conversation for a resource
   * @param resource - The resource to start a conversation for
   * @returns void
   */
  const startConversationForResource = async (resource: Resource) => {
    if (!resource) {
      return;
    }

    const instructions: Part[] = [
      {
        text: `Analyzing file "${resource.name}" (ID: ${resource.id})`,
      },
      ...formatInstructionForResource(resource),
    ];

    await startConversation(instructions);
  };

  /**
   * Start a conversation for a project
   * @param project - The project to start a conversation for
   * @returns void
   */
  const startConversationForProject = async (project: ProjectDetails) => {
    if (!project) {
      return;
    }

    const resources = project.resources ?? [];

    const instructions: Part[] = [
      {
        text: `Project "${project.name}" (ID: ${project.id})`,
      },
      {
        text: 'Project contents:',
      },
      ...(project.folders.length > 0
        ? [
            {
              text:
                'Folders:\n' + project.folders.map((f) => `- "${f.name}" (ID: ${f.id})`).join('\n'),
            },
          ]
        : []),
      ...(resources.length > 0
        ? [
            {
              text: 'Files:\n' + resources.map((r) => `- "${r.name}" (ID: ${r.id})`).join('\n'),
            },
            ...resources.flatMap(formatInstructionForResource),
          ]
        : []),
    ];

    await startConversation(instructions);
  };

  return {
    startConversationWithResources,
    startConversationForResource,
    startConversationForProject,
    sendMessage,
    updateContextWithNewResources,
    removeResourcesFromContext,
    isReplying,
    messages,
    isStartingConversation,
    allowSendMessage: session && !isReplying,
    hasActiveSession: !!session,
    activeResources,
  };
};

export default useAiAgent;