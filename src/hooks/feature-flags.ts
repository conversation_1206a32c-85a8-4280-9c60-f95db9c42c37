import { useState, useEffect } from 'react';
import { usePostHog } from 'posthog-js/react';

import { AppFeatures } from 'src/types';

const useFeatureFlags = () => {
  const posthog = usePostHog();
  const [featureFlags, setFeatureFlags] = useState<AppFeatures[]>([]);

  const isFlagEnabled = (flag: AppFeatures) => featureFlags.includes(flag);

  useEffect(() => {
    posthog.onFeatureFlags((flags) => setFeatureFlags(flags as AppFeatures[]));
  }, []);

  return {
    featureFlags,
    isFlagEnabled,
    hasDevFeature: isFlagEnabled(AppFeatures.INTERNAL),
    hasNewLayout: isFlagEnabled(AppFeatures.NEW_LAYOUT),
    hasNewResourceDetailsView: isFlagEnabled(AppFeatures.NEW_RESOURCE_DETAILS_VIEW),
  };
};

export default useFeatureFlags;
