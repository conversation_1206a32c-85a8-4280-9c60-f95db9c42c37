import { useGetUserStatisticsQuery } from 'src/store/api/user';

const useUserStatistics = () => {
  const { data: statistics } = useGetUserStatisticsQuery({});
  if (!statistics)
    return {
      totalDurationsInMins: 0,
      totalResourcesCount: 0,
      totalTranscriptionsWordCount: 0,
    };

  const { totalResourcesCount, totalResourcesDuration, totalTranscriptionsWordCount } = statistics;
  const totalDurationsInMins = Math.floor(totalResourcesDuration / 60);

  return {
    totalDurationsInMins,
    totalResourcesCount,
    totalTranscriptionsWordCount,
  };
};

export default useUserStatistics;
