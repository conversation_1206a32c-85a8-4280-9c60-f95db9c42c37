import { logEvent } from 'firebase/analytics';
import { usePostHog } from 'posthog-js/react';

import { getUtmParams } from 'src/utils/url';
import { extractEmailDomain } from 'src/utils/email';

import { firebaseAnalytics } from 'src/lib/firebase';

interface IdentifyOptions {
  userId?: string;
  email?: string;
  displayName?: string;
  createdDate?: Date;
}

interface TrackEventOptions {
  // Typically the object that was interacted with (e.g. 'Video')
  eventCategory: string;
  // The action taken (e.g. 'Play')
  eventAction: string;
  // Useful for categorizing events (e.g. 'Document')
  eventLabel?: string;
  // Optional properties
  properties?: Record<string, any>;
}

const useAnalytics = () => {
  const posthog = usePostHog();

  const trackEvent = (data: TrackEventOptions) => {
    const { eventCategory, eventAction, eventLabel, properties = {} } = data;
    const utmParams = getUtmParams();

    posthog.capture(eventAction, {
      eventCategory,
      eventAction,
      eventLabel,
      ...utmParams,
      ...properties,
    });
  };

  const identify = (options: IdentifyOptions = {}) => {
    const { userId, email, displayName = '', createdDate } = options;
    if (!userId) return;

    posthog.identify(userId, {
      userId,
      email,
      emailDomain: extractEmailDomain(email),
      displayName,
      createdDate,
    });
  };

  const trackFirebaseEvent = (event: string, params: Record<string, any>) => {
    logEvent(firebaseAnalytics, event, params);
  };

  return { trackEvent, identify, trackFirebaseEvent };
};

export default useAnalytics;
