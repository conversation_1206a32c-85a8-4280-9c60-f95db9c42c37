/* eslint-disable consistent-return */

import type { Part } from "firebase/ai"
import type { LiveTranscriptionSegment } from "src/types"

import { throttle } from "lodash"
import { useRef, useState } from "react"

import { geminiModel } from "src/lib/firebase"

const getFollowUpQuestions = async (transcript: string, systemInstruction: Part[]): Promise<string> => {
  if (!transcript) return "Transcript is empty."
  if (!geminiModel) return "AI model not initialized."

  try {

    const SYSTEM_INSTRUCTION: Part[] = [
      {
        text: "You are a helpful assistant. Given the meeting transcript provided by the user, identify relevant follow-up questions that should be asked based on the transcript."
      },
      {
        text: "If there are no follow-up questions, can ignore the request and respond with 'No follow-up questions.'"
      }
    ]
    const streamingResponse = await geminiModel.generateContentStream({
      systemInstruction: {
        parts: systemInstruction.length ? systemInstruction : SYSTEM_INSTRUCTION,
        role: "system"
      },
      contents: [
        {
          role: "user" as const,
          parts: [
            {
              text: transcript
            }
          ]
        }
      ]
    })
    let fullText = ""

    // Process each chunk of the stream
    for await (const chunk of streamingResponse.stream) {
      const chunkText = chunk.text()
      fullText += chunkText
    }

    return fullText.trim()
  } catch (error) {
    console.error("Error generating follow-up questions:", error)
    if (error instanceof Error) {
      // More detailed error handling
      if (error.message?.includes("not found")) {
        return "Failed: AI model configuration not found. Please ensure Vertex AI API is enabled."
      } else if (error.message?.includes("permission")) {
        return "Failed: Permission denied. Check your API key and permissions."
      } else if (error.message?.includes("quota")) {
        return "Failed: API quota exceeded. Try again later."
      }
    }

    return "Failed to generate follow-up questions."
  }
}
function processTranscripts(
  transcripts: LiveTranscriptionSegment[]
): string {
  if (!transcripts || transcripts.length === 0) {
    throw new Error("No transcripts provided")
  }
  // Combine all transcripts into a single text with proper spacing
  return transcripts.map((t) => t.text.trim()).join(" ")
}
export function useAiSuggestions() {
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])
  const lastProcessedTranscriptIndexRef = useRef<number>(-1)

  const throttledGetQuestions = useRef(

    throttle(
      async (newTranscripts: LiveTranscriptionSegment[], startIndex: number): Promise<void> => {
        const allTranscripts = newTranscripts;
        const newTranscriptsOnly = newTranscripts.slice(startIndex);

        const fullContext = processTranscripts(allTranscripts);
        const newContent = processTranscripts(newTranscriptsOnly);

        console.log("Generating follow-up questions:", {
          startIndex,
          totalTranscripts: allTranscripts.length,
          newTranscriptsCount: newTranscriptsOnly.length,
          hasContext: fullContext.length > 0,
          hasNewContent: newContent.length > 0,
        });

        const maxRetries = 3;
        let retryCount = 0;

        const tryGetQuestions = async (): Promise<void> => {
          try {
            const questions = await getFollowUpQuestions(fullContext, []);
            console.log("Follow-up questions received:", questions);

            const questionsArray = questions
              .split(/[\n\r]+/)
              .map((q) => q.trim())
              .filter((q) => q.length > 0 && q !== "No follow-up questions.");

            if (questionsArray.length === 0) {
              throw new Error("No valid questions generated");
            }

            setFollowUpQuestions(questionsArray);
            lastProcessedTranscriptIndexRef.current = newTranscripts.length - 1;
          } catch (error) {
            console.error("Failed to get questions:", error);

            if (retryCount < maxRetries) {
              retryCount++;
              console.log(`Retrying (${retryCount}/${maxRetries})...`);
              await new Promise((resolve) =>
                setTimeout(resolve, Math.pow(2, retryCount) * 1000)
              );
              return tryGetQuestions();
            }

            console.log("Max retries reached, keeping existing questions");
          }

        };

        return tryGetQuestions(); 
      },
      3000
    )

  ).current

  const handleTranscriptsChange = (newTranscripts: LiveTranscriptionSegment[]) => {
    if (newTranscripts.length > lastProcessedTranscriptIndexRef.current + 1) {
      throttledGetQuestions(
        newTranscripts,
        lastProcessedTranscriptIndexRef.current + 1
      )
    }
  }





  return {
    followUpQuestions,
    handleTranscriptsChange,
  }
}
