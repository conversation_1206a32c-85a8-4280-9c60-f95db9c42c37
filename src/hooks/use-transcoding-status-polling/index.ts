import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { useLazyTranscodingStatusQuery } from 'src/store/api/resources';

import type { UseTranscodingStatusPollingProps } from './types';

/**
 * Custom hook to poll for transcoding status of resources
 * @param resources - Array of resources to check for transcoding status
 * @param pollingInterval - Interval between polls in ms (default: 5000)
 * @param errorRetryInterval - Interval between retries on error in ms (default: 10000)
 * @returns Object containing isPolling state
 */
export const useTranscodingStatusPolling = ({
  projectId: projectIdProp,
  resources,
  pollingInterval = 10_000,
  errorRetryInterval = 10_000,
}: UseTranscodingStatusPollingProps) => {
  const [isPolling, setIsPolling] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get the dispatch function to manually trigger the query
  const [trigger] = useLazyTranscodingStatusQuery();

  const clearPollingTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const { transcodingResourceIds, projectId } = useMemo(() => {
    const ids = resources
      .filter((resource) => resource.isTranscoding)
      .map((resource) => resource.id);

    return { transcodingResourceIds: ids, projectId: projectIdProp };
  }, [resources, projectIdProp]);

  const startPolling = useCallback(async () => {
    // Clear any existing timeout
    clearPollingTimeout();

    // If no resources are transcoding, stop polling
    if (transcodingResourceIds.length === 0) {
      setIsPolling(false);
      return;
    }

    setIsPolling(true);

    try {
      // Trigger the query manually
      const result = await trigger({
        id: projectId,
        payload: { ids: transcodingResourceIds },
      }).unwrap();

      // Check if any resources are still transcoding
      const hasTranscodingResources = result.some((item) => item.isTranscoding);

      // If no resources are transcoding anymore, stop polling
      if (!hasTranscodingResources) {
        clearPollingTimeout();
        setIsPolling(false);
        return;
      }

      // Schedule next poll after pollingInterval
      timeoutRef.current = setTimeout(startPolling, pollingInterval);
    } catch {
      // On error, retry after errorRetryInterval
      timeoutRef.current = setTimeout(startPolling, errorRetryInterval);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [transcodingResourceIds, projectId]);

  // Start or stop polling when resources change
  useEffect(() => {
    const transcodingResourcesExist = transcodingResourceIds.length > 0;

    if (transcodingResourcesExist && !isPolling) {
      // Start polling if there are transcoding resources and we're not already polling
      startPolling();
    } else if (!transcodingResourcesExist && isPolling) {
      // Stop polling if there are no transcoding resources but we're still polling
      clearPollingTimeout();
      setIsPolling(false);
    }
  }, [transcodingResourceIds, isPolling, startPolling, clearPollingTimeout, projectId]);

  return { isPolling };
};
