import type { GenerativeModel} from 'firebase/vertexai';

import { getAuth } from 'firebase/auth';
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAnalytics } from 'firebase/analytics';
import { getAI, getGenerativeModel, VertexAIBackend } from 'firebase/vertexai';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

export const firebaseApp = initializeApp(CONFIG.firebase);

export const AUTH = getAuth(firebaseApp);

export const FIRESTORE = getFirestore(firebaseApp);

export const VERTEXAI = getAI(firebaseApp, {
  backend: new VertexAIBackend('global'),
});

export const geminiModel: GenerativeModel = getGenerativeModel(VERTEXAI, {
      model: CONFIG.firebase.geminiModel,
      generationConfig: {
        temperature: 0.3, // Lower temperature for more consistent results
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024
      }
    });
export const firebaseAnalytics = getAnalytics(firebaseApp);
