import type { AxiosRequestConfig } from 'axios';
import type { ApiRequestArgs } from 'src/store/api/types';

import qs from 'qs';
import axios from 'axios';
import { pick } from 'es-toolkit';

import { CONFIG } from 'src/global-config';

import { AUTH } from './firebase';

// ----------------------------------------------------------------------

const axiosInstance = axios.create({ baseURL: CONFIG.aidaApiUrl });

axiosInstance.interceptors.request.use(
  async (config) => {
    const token = await AUTH.currentUser?.getIdToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export default axiosInstance;

export const baseApiCall = async <T>(args: ApiRequestArgs) => {
  const {
    uri,
    headers,
    method = 'GET',
    data = null,
    params,
    responseType = 'json',
    ...config
  } = args;

  const request: AxiosRequestConfig = {
    url: uri,
    headers: {
      Accept: 'application/json',
      ...headers,
    },
    method,
    params,
    responseType,
    paramsSerializer: (param) => qs.stringify(param),
    ...pick(config, ['cancelToken', 'onUploadProgress']),
  };

  if (data !== null) {
    request.data = data;
  }
  const response = await axiosInstance(request);
  return response.data ? (response.data as T) : null;
};
