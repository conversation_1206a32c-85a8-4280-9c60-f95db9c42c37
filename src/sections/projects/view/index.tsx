import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useBoolean } from 'minimal-shared/hooks';

import { Box, Stack, Button, Typography, IconButton } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import useFeatureFlags from 'src/hooks/feature-flags';

import { DashboardContent } from 'src/layouts/dashboard';
import { useGetProjectsQuery } from 'src/store/api/projects';
import { ProjectIllustration } from 'src/assets/illustrations';
import { selectLastViewedProjectId } from 'src/store/slices/settings/selectors';

import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

import ProjectsList from '../components/projects-list';
import CreateProjectDialog from '../components/create-project-dialog';

const ProjectEmptyView: React.FC<{ onCreateProject: () => void }> = ({ onCreateProject }) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      gap: 2,
      justifyContent: 'center',
      alignItems: 'center',
      marginY: 'auto',
    }}
  >
    <ProjectIllustration sx={{ width: 700, height: '100%' }} />
    <Button
      startIcon={
        <Iconify icon="fluent:add-circle-12-filled" width={24} sx={{ color: 'primary.main' }} />
      }
      sx={{ fontSize: 20 }}
      size="large"
      onClick={onCreateProject}
    >
      Create a project to start
    </Button>
  </Box>
);

const ProjectsView: React.FC = () => {
  const router = useRouter();
  const { data: projects, isLoading } = useGetProjectsQuery({});
  const { hasNewLayout } = useFeatureFlags();
  const createProjectDialog = useBoolean();
  const lastViewedProjectId = useSelector(selectLastViewedProjectId);

  const isEmpty = !projects || projects.length === 0;

  useEffect(() => {
    if (!hasNewLayout) return;

    // For new layout, we will redirect to the last viewed project or the first project
    if (!isLoading && !isEmpty) {
      // If there's a last viewed project and it exists in the current projects list
      if (lastViewedProjectId && projects?.some((p) => p.id === lastViewedProjectId)) {
        router.push(paths.project.details(lastViewedProjectId));
      }
      // Otherwise, redirect to the first project
      else if (projects && projects.length > 0) {
        router.push(paths.project.details(projects[0].id));
      }
    }
  }, [isLoading, isEmpty, projects, lastViewedProjectId, router, hasNewLayout]);

  if (isLoading) return <LoadingScreen />;

  // For new layout, we don't render the projects list since we'll redirect
  if (hasNewLayout && !isEmpty) {
    return <LoadingScreen />;
  }

  return (
    <>
      <DashboardContent maxWidth="xl" sx={{ width: '100%' }}>
        {isEmpty ? (
          <ProjectEmptyView onCreateProject={createProjectDialog.onTrue} />
        ) : (
          <Stack direction="column" gap={3}>
            {/* Header */}
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Stack direction="row" gap={1} alignItems="center">
                <Typography variant="h4">Projects</Typography>
                <IconButton size="small" onClick={createProjectDialog.onTrue}>
                  <Iconify
                    icon="fluent:add-circle-12-filled"
                    width={24}
                    sx={{ color: 'primary.main' }}
                  />
                </IconButton>
              </Stack>
            </Stack>
            {/* Projects list */}
            <ProjectsList data={projects} />
          </Stack>
        )}
      </DashboardContent>

      <CreateProjectDialog open={createProjectDialog.value} onClose={createProjectDialog.onFalse} />
    </>
  );
};

export default ProjectsView;
