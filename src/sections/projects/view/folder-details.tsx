import { useParams } from 'src/routes/hooks';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { AUTH } from 'src/lib/firebase';
import { DashboardContent } from 'src/layouts/dashboard';
import {
  ProjectRole,
  useGetProjectMembershipQuery,
  useGetProjectFolderDetailsQuery,
} from 'src/store/api/projects';

import { LoadingScreen } from 'src/components/loading-screen';

import FilesSection from '../components/files-section';

const ProjectFolderDetailsView: React.FC = () => {
  useUserInitialContext();
  const { id = '', folderId = '' } = useParams();

  const { data, isLoading } = useGetProjectFolderDetailsQuery(
    { id, folderId },
    {
      skip: !id || !folderId,
    }
  );

  const { data: projectMembership } = useGetProjectMembershipQuery(
    { id },
    {
      skip: !id,
    }
  );

  const userId = AUTH.currentUser?.uid ?? 'user';
  const canEdit =
    projectMembership?.role === ProjectRole.EDITOR || data?.folder?.createdById === userId;

  if (isLoading || !data) return <LoadingScreen />;

  return (
    <DashboardContent maxWidth="xl" sx={{ width: '100%' }}>
      <FilesSection projectId={id} files={data?.resources ?? []} canEdit={canEdit} />
    </DashboardContent>
  );
};

export default ProjectFolderDetailsView;
