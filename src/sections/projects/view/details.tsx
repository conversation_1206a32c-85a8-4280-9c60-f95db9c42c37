import { Stack } from '@mui/material';

import { AUTH } from 'src/lib/firebase';
import { DashboardContent } from 'src/layouts/dashboard';
import {
  ProjectRole,
  useGetProjectDetailsQuery,
  useGetProjectMembershipQuery,
} from 'src/store/api/projects';

import { LoadingScreen } from 'src/components/loading-screen';

import FilesSection from '../components/files-section';

const ProjectDetailsView: React.FC<{ projectId: string }> = ({ projectId }) => {
  const userId = AUTH.currentUser?.uid ?? 'user';
  const {
    data: project,
    isLoading,
    isFetching,
  } = useGetProjectDetailsQuery(
    { id: projectId },
    {
      skip: !projectId,
      refetchOnFocus: false,
    }
  );

  const { data: projectMembership } = useGetProjectMembershipQuery(
    { id: projectId },
    {
      skip: !projectId,
      refetchOnFocus: false,
    }
  );

  const canEdit = projectMembership?.role === ProjectRole.EDITOR || project?.createdById === userId;

  if (isLoading || isFetching || !project) return <LoadingScreen />;

  return (
    <DashboardContent maxWidth="xl" sx={{ width: '100%' }}>
      <Stack
        direction="column"
        gap={2}
        sx={{
          mt: 2,
        }}
      >
        <FilesSection projectId={projectId} files={project.resources} canEdit={canEdit} />
      </Stack>
    </DashboardContent>
  );
};

export default ProjectDetailsView;
