import { useBoolean } from 'minimal-shared/hooks';
import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { useParams } from 'src/routes/hooks';

import {
  ProjectRole,
  RequestStatus,
  InvitationStatus,
  useInviteUserMutation,
  usePublishShareLinkMutation,
  useResendInvitationMutation,
  useCancelInvitationMutation,
  useRemoveProjectMemberMutation,
  useApproveOrRejectAccessMutation,
  useLazyGetListAccessRequestsQuery,
  useLazyGetListProjectMembersQuery,
  useChangeRoleOfProjectMemberMutation,
  useLazyGetListPendingInvitationsQuery,
} from 'src/store/api/projects';

import { toast } from 'src/components/snackbar';

import { InviteContext } from './invite-context';

import type { InviteProviderProps } from '../types';
// ----------------------------------------------------------------------

export function InviteProvider({ children }: InviteProviderProps) {
  const { value: open, onTrue: onOpenDialog, onFalse: onClose } = useBoolean();
  const { id: projectId } = useParams();
  const oldProjectId = useRef(projectId);

  // Loading states for individual actions
  const [isApprovingOrRejecting, setIsApprovingOrRejecting] = useState<Record<string, string>>({});
  const [isRemoving, setIsRemoving] = useState<Record<string, boolean>>({});
  const [isResending, setIsResending] = useState<Record<string, boolean>>({});
  const [isCanceling, setIsCanceling] = useState<Record<string, boolean>>({});

  const [inviteUserMutation] = useInviteUserMutation();
  const [changeUserRoleMutation] = useChangeRoleOfProjectMemberMutation();
  const [publishShareLinkMutation] = usePublishShareLinkMutation();
  const [approveOrRejectAccessMutation] = useApproveOrRejectAccessMutation();
  const [removeProjectMemberMutation] = useRemoveProjectMemberMutation();
  const [resendInvitationMutation] = useResendInvitationMutation();
  const [cancelInvitationMutation] = useCancelInvitationMutation();

  // Reset states when project changes
  useEffect(() => {
    setIsApprovingOrRejecting({});
    setIsRemoving({});
    setIsResending({});
    setIsCanceling({});
  }, [projectId]);

  // Lazy query hooks
  const [
    triggerGetProjectMembers,
    { data: projectMembers, isLoading: isMembersLoading, isFetching: isMembersFetching },
  ] = useLazyGetListProjectMembersQuery();

  const [
    triggerGetPendingInvitations,
    {
      data: pendingInvitations,
      isLoading: isInvitationsLoading,
      isFetching: isInvitationsFetching,
    },
  ] = useLazyGetListPendingInvitationsQuery();

  const [
    triggerGetAccessRequests,
    {
      data: accessRequests,
      isLoading: isAccessRequestsLoading,
      isFetching: isAccessRequestsFetching,
    },
  ] = useLazyGetListAccessRequestsQuery();

  // Custom onOpen function that triggers all queries
  const onOpen = useCallback(() => {
    if (projectId) {
      triggerGetProjectMembers({
        id: projectId,
      });

      triggerGetPendingInvitations({
        id: projectId,
        query: {
          status: InvitationStatus.SENT,
        },
      });

      triggerGetAccessRequests({
        id: projectId,
        query: {
          status: RequestStatus.PENDING,
        },
      });
      onOpenDialog();
    }
  }, [
    projectId,
    onOpenDialog,
    triggerGetProjectMembers,
    triggerGetPendingInvitations,
    triggerGetAccessRequests,
  ]);

  useEffect(() => {
    if (window.location.hash === '#sharing' && !open) {
      onOpen();
      window.location.hash = '';
    }
  }, [open, onOpen, window.location.hash]);

  // Combine loading states and handle project change
  const isLoading = useMemo(() => {
    if (!projectId || !open) return false;
    if (
      oldProjectId.current !== projectId &&
      (isMembersFetching || isInvitationsFetching || isAccessRequestsFetching)
    ) {
      oldProjectId.current = projectId;
      return isMembersFetching || isInvitationsFetching || isAccessRequestsFetching;
    }
    return isMembersLoading || isInvitationsLoading || isAccessRequestsLoading;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    open,
    isMembersLoading,
    isInvitationsLoading,
    isAccessRequestsLoading,
    isMembersFetching,
    isInvitationsFetching,
    isAccessRequestsFetching,
  ]);

  const inviteUser = useCallback(
    async (email: string) => {
      if (!projectId) {
        toast.error('Cannot invite users without a project');
        return;
      }

      try {
        await inviteUserMutation({
          id: projectId,
          payload: {
            email,
            role: ProjectRole.VIEWER, // Default role for new invitees
          },
          query: {
            force: true,
          },
        }).unwrap();

        toast.success(`Invitation sent to ${email}`);
      } catch (error) {
        toast.error('Failed to send invitation');
        console.error(error);
      }
    },
    [projectId, inviteUserMutation]
  );

  // Change user role
  const changeUserRole = useCallback(
    async (userId: string, role: ProjectRole) => {
      if (!projectId) {
        toast.error('Cannot change user role without a project');
        return;
      }

      try {
        await changeUserRoleMutation({
          id: projectId,
          memberId: userId,
          payload: { role },
        }).unwrap();

        toast.success('User role changed');
      } catch (error) {
        toast.error('Failed to change user role');
        console.error(error);
      }
    },
    [projectId, changeUserRoleMutation]
  );

  const removeMember = useCallback(
    async (userId: string) => {
      if (!projectId) {
        toast.error('Cannot remove user without a project');
        return;
      }

      try {
        setIsRemoving((prev) => ({ ...prev, [userId]: true }));

        await removeProjectMemberMutation({
          id: projectId,
          memberId: userId,
        }).unwrap();

        toast.success('User removed from project');
      } catch (error) {
        toast.error('Failed to remove user');
        console.error(error);
      } finally {
        setIsRemoving((prev) => ({ ...prev, [userId]: false }));
      }
    },
    [projectId, removeProjectMemberMutation]
  );

  const resendInvitation = useCallback(
    async (email: string) => {
      if (!projectId) {
        toast.error('Cannot resend invitation without a project');
        return;
      }

      try {
        setIsResending((prev) => ({ ...prev, [email]: true }));

        await resendInvitationMutation({
          id: projectId,
          payload: { email },
        }).unwrap();

        toast.success(`Invitation resent to ${email}`);
      } catch (error) {
        toast.error('Failed to resend invitation');
        console.error(error);
      } finally {
        setIsResending((prev) => ({ ...prev, [email]: false }));
      }
    },
    [projectId, resendInvitationMutation]
  );

  const cancelInvitation = useCallback(
    async (inviteId: string, email: string) => {
      if (!projectId) {
        toast.error('Cannot cancel invitation without a project');
        return;
      }

      try {
        setIsCanceling((prev) => ({ ...prev, [inviteId]: true }));

        await cancelInvitationMutation({
          id: projectId,
          inviteId,
        }).unwrap();

        toast.success('Invitation canceled');
      } catch {
        toast.error('Failed to cancel invitation');
      } finally {
        setIsCanceling((prev) => ({ ...prev, [inviteId]: false }));
      }
    },
    [projectId, cancelInvitationMutation]
  );

  const approveAccessRequest = useCallback(
    async (accessRequestId: string, status: RequestStatus) => {
      if (!projectId) {
        toast.error('Cannot approve access request without a project');
        return;
      }

      try {
        setIsApprovingOrRejecting((prev) => ({ ...prev, [accessRequestId]: status }));

        const isApproved = status === RequestStatus.APPROVED;

        await approveOrRejectAccessMutation({
          id: projectId,
          accessRequestId,
          payload: { isApproved },
        }).unwrap();

        toast.success(isApproved ? 'Access request approved' : 'Access request rejected');
      } catch (error) {
        toast.error('Failed to process access request');
        console.error(error);
      } finally {
        // remove the status from the record
        setIsApprovingOrRejecting((prev) => {
          const newState = { ...prev };
          delete newState[accessRequestId];
          return newState;
        });
      }
    },
    [projectId, approveOrRejectAccessMutation]
  );

  const copyInviteLink = useCallback(async () => {
    if (!projectId) {
      toast.error('Cannot copy invite link without a project');
      return;
    }

    const shareLink = await publishShareLinkMutation({
      id: projectId!,
    }).unwrap();

    const link = `${window.location.origin}/invite-project/${projectId || 'default'}?token=${shareLink.token}`;

    navigator.clipboard
      .writeText(link)
      .then(() => {
        toast.success('Link copied to clipboard');
      })
      .catch(() => {
        toast.error('Failed to copy link');
      });
  }, [projectId, publishShareLinkMutation]);

  const contextValue = useMemo(
    () => ({
      open,
      onOpen,
      onClose,
      users: projectMembers || [],
      pendingInvitations: pendingInvitations || [],
      accessRequests: accessRequests || [],
      isLoading,
      isApprovingOrRejecting,
      isRemoving,
      isResending,
      isCanceling,
      inviteUser,
      changeUserRole,
      copyInviteLink,
      approveAccessRequest,
      removeMember,
      resendInvitation,
      cancelInvitation,
    }),
    [
      open,
      onOpen,
      onClose,
      projectMembers,
      pendingInvitations,
      accessRequests,
      isLoading,
      isApprovingOrRejecting,
      isRemoving,
      isResending,
      isCanceling,
      inviteUser,
      changeUserRole,
      copyInviteLink,
      approveAccessRequest,
      removeMember,
      resendInvitation,
      cancelInvitation,
    ]
  );

  return <InviteContext.Provider value={contextValue}>{children}</InviteContext.Provider>;
}
