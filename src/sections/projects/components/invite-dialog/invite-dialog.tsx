import type { ProjectRole } from 'src/store/api/projects';

import { useState } from 'react';

import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import InputBase from '@mui/material/InputBase';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import DialogContentText from '@mui/material/DialogContentText';

import { Iconify } from 'src/components/iconify';

import { useInviteContext } from './context';
import { MembersList, InvitationsList, AccessRequestsList } from './components/list-components';

import type { InviteDialogProps } from './types';

// ----------------------------------------------------------------------

export function InviteDialog({ title = 'Invite' }: InviteDialogProps) {
  const {
    open,
    onClose,
    users,
    pendingInvitations,
    accessRequests,
    inviteUser,
    changeUserRole,
    copyInviteLink,
    approveAccessRequest,
    removeMember,
    resendInvitation,
    cancelInvitation,
    isLoading,
    isApprovingOrRejecting,
    isRemoving,
    isResending,
    isCanceling,
  } = useInviteContext();

  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<{ userId: string; name: string } | null>(null);
  const [cancelConfirmOpen, setCancelConfirmOpen] = useState(false);
  const [inviteToCancel, setInviteToCancel] = useState<{ inviteId: string; email: string } | null>(
    null
  );

  const handleChangeEmail = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(event.target.value);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleInvite();
    }
  };

  const handleInvite = async () => {
    if (!email) return;

    setIsSubmitting(true);

    await inviteUser(email);

    // Reset form after submission
    setEmail('');
    setIsSubmitting(false);
  };

  const handleRoleChange = (userId: string, value: string | ProjectRole, userName: string) => {
    if (value === 'DELETE') {
      setUserToDelete({ userId, name: userName });
      setDeleteConfirmOpen(true);
    } else {
      changeUserRole(userId, value as ProjectRole);
    }
  };

  const handleConfirmDelete = () => {
    if (userToDelete && removeMember) {
      removeMember(userToDelete.userId);
    }
    setDeleteConfirmOpen(false);
    setUserToDelete(null);
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setUserToDelete(null);
  };

  const handleCancelInvitation = (inviteId: string, inviteEmail: string) => {
    setInviteToCancel({ inviteId, email: inviteEmail });
    setCancelConfirmOpen(true);
  };

  const handleConfirmCancelInvitation = () => {
    if (inviteToCancel && cancelInvitation) {
      cancelInvitation(inviteToCancel.inviteId, inviteToCancel.email);
    }
    setCancelConfirmOpen(false);
    setInviteToCancel(null);
  };

  const handleCancelCancelInvitation = () => {
    setCancelConfirmOpen(false);
    setInviteToCancel(null);
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle sx={{ pb: 2 }}>{title}</DialogTitle>

      <DialogContent sx={{ pt: 1, pb: 3 }}>
        <Paper
          component="form"
          sx={{
            p: '8px 6px',
            display: 'flex',
            alignItems: 'center',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'divider',
            mb: 3,
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Email"
            value={email}
            onChange={handleChangeEmail}
            onKeyDown={handleKeyDown}
          />
          <LoadingButton
            loading={isSubmitting}
            variant="contained"
            sx={{
              borderRadius: 1,
              mx: 1,
              px: 2,
              py: 1,
              bgcolor: 'action.disabledBackground',
              color: 'text.primary',
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
            onClick={handleInvite}
          >
            Send Invite
          </LoadingButton>
        </Paper>

        {/* Members List */}
        <MembersList
          data={users}
          loading={isLoading}
          onRoleChange={handleRoleChange}
          isRemoving={isRemoving}
        />

        {/* Pending Invitations List */}
        <InvitationsList
          data={pendingInvitations}
          loading={isLoading}
          title="Direct invitations"
          showDivider={users.length > 0}
          onResend={resendInvitation}
          isResending={isResending}
          onCancel={handleCancelInvitation}
          isCanceling={isCanceling}
        />

        {/* Access Requests List */}
        <AccessRequestsList
          data={accessRequests}
          loading={isLoading}
          title="Access Requests"
          showDivider={users.length > 0 || pendingInvitations.length > 0}
          onStatusChange={approveAccessRequest}
          isApprovingOrRejecting={isApprovingOrRejecting}
        />

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            startIcon={<Iconify icon="mingcute:link-line" />}
            sx={{ mr: 'auto' }}
            onClick={copyInviteLink}
          >
            Copy link
          </Button>
          <Button variant="outlined" color="inherit" onClick={onClose}>
            Close
          </Button>
        </Box>
      </DialogContent>

      {/* Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={handleCancelDelete}>
        <DialogTitle>Remove User</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to remove {userToDelete?.name} from this project?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>Cancel</Button>
          <LoadingButton
            onClick={handleConfirmDelete}
            color="error"
            loading={userToDelete ? isRemoving[userToDelete.userId] : false}
            autoFocus
          >
            Remove
          </LoadingButton>
        </DialogActions>
      </Dialog>

      {/* Cancel Invitation Confirmation Dialog */}
      <Dialog open={cancelConfirmOpen} onClose={handleCancelCancelInvitation}>
        <DialogTitle>Cancel Invitation</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to cancel the invitation for{' '}
            <Typography component="span" sx={{ fontWeight: 600, color: 'text.primary', display: 'inline-block' }}>
              {inviteToCancel?.email}
            </Typography>
            ?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelCancelInvitation}>Discard</Button>
          <LoadingButton
            onClick={handleConfirmCancelInvitation}
            color="error"
            loading={inviteToCancel ? isCanceling[inviteToCancel.inviteId] : false}
            autoFocus
          >
            Confirm
          </LoadingButton>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
}
