import {
  List,
  Stack,
  Avatar,
  Select,
  Divider,
  MenuItem,
  ListItem,
  Typography,
  FormControl,
  ListItemText,
  ListItemAvatar,
  CircularProgress,
} from '@mui/material';

import { RequestStatus } from 'src/store/api/projects';

import { Iconify } from 'src/components/iconify';

import type { AccessRequestsListProps } from './types';

// ----------------------------------------------------------------------

export function AccessRequestsList({
  data,
  title,
  loading,
  onStatusChange,
  isApprovingOrRejecting = {},
  showDivider = false,
}: AccessRequestsListProps) {
  if (loading) {
    return null;
  }

  if (data.length === 0) {
    return null;
  }

  return (
    <>
      {showDivider && <Divider />}
      {title && (
        <Typography variant="subtitle2" sx={{ mt: 1 }}>
          {title}
        </Typography>
      )}
      <List
        sx={{
          width: '100%',
          maxHeight: '300px',
          overflow: 'auto',
        }}
      >
        {data.map((request) => (
          <ListItem
            key={request.id}
            secondaryAction={
              <Stack direction="row" alignItems="center" spacing={1}>
                {isApprovingOrRejecting[request.id] ? (
                  <>
                    <CircularProgress size={24} />
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {isApprovingOrRejecting[request.id] === RequestStatus.APPROVED
                        ? 'Approving...'
                        : 'Rejecting...'}
                    </Typography>
                  </>
                ) : (
                  <FormControl size="small" sx={{ minWidth: 120 }}>
                    <Select
                      value={request.status}
                      onChange={(e) => onStatusChange(request.id, e.target.value as RequestStatus)}
                      variant="outlined"
                      IconComponent={(props) => <Iconify icon="mingcute:down-line" {...props} />}
                      sx={{
                        '.MuiOutlinedInput-notchedOutline': {
                          border: 'none',
                        },
                        borderRadius: 0,
                      }}
                    >
                      <MenuItem disabled value={RequestStatus.PENDING}>
                        Pending
                      </MenuItem>
                      <MenuItem value={RequestStatus.APPROVED}>Approved</MenuItem>
                      <MenuItem value={RequestStatus.REJECTED}>Rejected</MenuItem>
                    </Select>
                  </FormControl>
                )}
              </Stack>
            }
          >
            <ListItemAvatar>
              <Avatar alt={request.user.displayName} src={request.user.photoURL}>
                {request.user.displayName.charAt(0).toUpperCase()}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={
                <Typography variant="subtitle2" noWrap>
                  {request.user.displayName}
                </Typography>
              }
              secondary={
                <Typography variant="body2" noWrap sx={{ color: 'text.secondary' }}>
                  {request.user.email}
                </Typography>
              }
            />
          </ListItem>
        ))}
      </List>
    </>
  );
}
