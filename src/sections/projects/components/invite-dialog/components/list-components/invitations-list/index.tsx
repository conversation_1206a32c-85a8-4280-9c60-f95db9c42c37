import {
  <PERSON>,
  <PERSON>,
  <PERSON>ack,
  alpha,
  Avatar,
  Button,
  <PERSON>vider,
  ListItem,
  useTheme,
  Typography,
  IconButton,
  ListItemText,
  ListItemAvatar,
  CircularProgress,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { getRoleDisplay } from '../utils';

import type { InvitationsListProps } from './types';

// ----------------------------------------------------------------------

export function InvitationsList({
  data,
  title,
  loading,
  showDivider = false,
  onResend,
  isResending = {},
  onCancel,
  isCanceling = {},
}: InvitationsListProps) {
  const theme = useTheme();

  const renderExpiredChip = () => (
    <Chip
      size="small"
      label="Expired"
      icon={<Iconify icon="solar:clock-circle-bold" width={14} />}
      sx={{
        color: theme.palette.mode === 'dark' ? '#fff' : 'error.main',
        bgcolor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.15 : 0.08),
        border: '1px solid',
        borderColor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
        '& .MuiChip-icon': {
          color: theme.palette.mode === 'dark' ? '#fff' : 'error.main',
        },
        '& .MuiChip-label': {
          fontWeight: 600,
          fontSize: '0.75rem',
          color: theme.palette.mode === 'dark' ? '#fff' : 'error.main',
        },
        // Professional hover effects
        transition: theme.transitions.create(['background-color', 'border-color', 'transform'], {
          duration: theme.transitions.duration.shorter,
        }),
        '&:hover': {
          bgcolor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.2 : 0.12),
          borderColor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.5 : 0.4),
          transform: 'scale(1.02)',
        },
      }}
    />
  );

  if (loading) {
    return null;
  }

  if (data.length === 0) {
    return null;
  }

  return (
    <>
      {showDivider && <Divider />}
      {title && (
        <Typography variant="subtitle2" sx={{ mt: 1 }}>
          {title}
        </Typography>
      )}
      <List
        sx={{
          width: '100%',
          maxHeight: '300px',
          overflow: 'auto',
        }}
      >
        {data.map((invitation) => (
          <ListItem
            key={invitation.id}
            secondaryAction={
              <Stack direction="row" alignItems="center" spacing={1}>
                {invitation.canResend &&
                  onResend &&
                  (isResending[invitation.id] ? (
                    <CircularProgress size={20} sx={{ color: 'primary.main' }} />
                  ) : (
                    <IconButton
                      size="small"
                      onClick={() => onResend(invitation.email)}
                      sx={{
                        color: theme.palette.mode === 'dark' ? '#fff' : 'primary.main',
                        cursor: 'pointer',
                        '&:hover': {
                          color: theme.palette.mode === 'dark' ? '#fff' : 'primary.main',
                        },
                      }}
                    >
                      <Iconify icon="mingcute:refresh-2-line" width={16} />
                    </IconButton>
                  ))}
                {invitation.isExpired ? (
                  renderExpiredChip()
                ) : (
                  <Typography
                    variant="body2"
                    noWrap
                    sx={{
                      color: 'text.secondary',
                      textTransform: 'capitalize',
                      minWidth: 'fit-content',
                    }}
                  >
                    {invitation.status.toLowerCase()}
                  </Typography>
                )}
                {onCancel &&
                  (isCanceling[invitation.id] ? (
                    <CircularProgress size={20} sx={{ color: 'error.main' }} />
                  ) : (
                    <Button
                      size="small"
                      variant="text"
                      onClick={() => onCancel(invitation.id, invitation.email)}
                      sx={{
                        minWidth: 'auto',
                        px: 1,
                        py: 0.5,
                        textTransform: 'none',
                        '&:hover': {
                          bgcolor: alpha(theme.palette.error.main, 0.08),
                          color: 'error.main'
                        },
                      }}
                    >
                      Cancel
                    </Button>
                  ))}
              </Stack>
            }
          >
            <ListItemAvatar>
              <Avatar alt={invitation.email} src={invitation.email}>
                {invitation.email.charAt(0).toUpperCase()}
              </Avatar>
            </ListItemAvatar>
            <ListItemText primary={invitation.email} secondary={getRoleDisplay(invitation.role)} />
          </ListItem>
        ))}
      </List>
    </>
  );
}
