import { List, ListItem, Skeleton, ListI<PERSON>Text, ListItemAvatar } from '@mui/material';

const ITEMS_COUNT = 2;

// ----------------------------------------------------------------------

export function ListSkeleton() {
  return (
    <List>
      {[...Array(ITEMS_COUNT)].map((_, index) => (
        <ListItem key={index}>
          <ListItemAvatar>
            <Skeleton variant="circular" width={40} height={40} />
          </ListItemAvatar>
          <ListItemText
            primary={<Skeleton variant="text" width="60%" />}
            secondary={<Skeleton variant="text" width="40%" />}
          />
          <Skeleton variant="rectangular" width={120} height={36} />
        </ListItem>
      ))}
    </List>
  );
}
