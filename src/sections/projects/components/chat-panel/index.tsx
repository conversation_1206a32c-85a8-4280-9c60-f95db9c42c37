import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';

import { useRef, useEffect } from 'react';

import { Paper, Stack, Button } from '@mui/material';

import useAiAgent from 'src/hooks/ai-agent';
import useResponsive from 'src/hooks/use-responsive';

import ChatBox from 'src/sections/chat/chat-box';

import ProjectActions from '../project-actions';

interface ChatPanelProps {
  selectedFiles: Resource[];
  projectId: string;
  sx?: SxProps;
}

const QUICK_REPLY_OPTIONS = ['Summarise', 'Discuss', 'Evaluate'];

const QuickReplyOptions: React.FC<{ onSelect: (option: string) => void }> = ({ onSelect }) => (
  <Stack direction="row" alignItems="center" gap={1}>
    {QUICK_REPLY_OPTIONS.map((option) => (
      <Button variant="outlined" size="small" key={option} onClick={() => onSelect(option)}>
        {option}
      </Button>
    ))}
  </Stack>
);

const ChatPanel: React.FC<ChatPanelProps> = ({ selectedFiles, projectId, sx = {} }) => {
  const { isMobile } = useResponsive();
  const {
    messages,
    isReplying,
    allowSendMessage,
    sendMessage,
    isStartingConversation,
    startConversationWithResources,
    updateContextWithNewResources,
    removeResourcesFromContext,
    hasActiveSession,
  } = useAiAgent();

  const prevSelectedFilesRef = useRef<Resource[]>([]);

  // Start conversation when component mounts
  useEffect(() => {
    if (!hasActiveSession) {
      startConversationWithResources([]);
    }
  }, [hasActiveSession, startConversationWithResources]);

  // Handle selected files updates
  useEffect(() => {
    const prevFiles = prevSelectedFilesRef.current;

    // Find new files that weren't in the previous selection
    const newFiles = selectedFiles.filter(
      (file) => !prevFiles.some((prevFile) => prevFile.id === file.id)
    );

    // Find files that were removed from the previous selection
    const removedFiles = prevFiles.filter(
      (prevFile) => !selectedFiles.some((file) => file.id === prevFile.id)
    );

    // Handle new files
    if (newFiles.length > 0) {
      updateContextWithNewResources(newFiles);
    }

    // Handle removed files
    if (removedFiles.length > 0) {
      removeResourcesFromContext(removedFiles);
    }

    // Update the ref with current selection
    prevSelectedFilesRef.current = selectedFiles;
  }, [selectedFiles, updateContextWithNewResources, removeResourcesFromContext]);

  return (
    <Paper
      elevation={1}
      sx={{
        overflow: {
          xs: 'auto',
          md: 'hidden',
        },
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '50%',
        },
        ...sx,
      }}
    >
      <Stack sx={{ width: '100%', direction: 'column', height: '100%' }}>
        <ChatBox
          containerSx={{
            display: 'flex',
            flexDirection: 'column',
            height: 'auto',
          }}
          messageListSx={{ height: { xs: 400, md: '70vh' }, pt: 2, px: 0, pr: 1 }}
          messages={messages}
          loading={isStartingConversation}
          isReplying={isReplying}
          disabled={!allowSendMessage}
          sendMessage={sendMessage}
        />
        <Stack direction="row" justifyContent="space-between">
          <ProjectActions projectId={projectId} />
          {!isMobile && <QuickReplyOptions onSelect={sendMessage} />}
        </Stack>
      </Stack>
    </Paper>
  );
};

export default ChatPanel;
