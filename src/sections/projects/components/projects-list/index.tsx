import type { Project } from 'src/types/project';

import { Box } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import ProjectCard from '../project-card';

const ProjectsList: React.FC<{ data: Project[] }> = ({ data }) => {
  const router = useRouter();

  const viewProject = (id: string) => {
    router.push(paths.project.details(id));
  };

  return (
    <Box
      sx={{
        gap: 3,
        display: 'grid',
        gridTemplateColumns: {
          xs: 'repeat(1, 1fr)',
          sm: 'repeat(2, 1fr)',
          md: 'repeat(3, 1fr)',
          lg: 'repeat(4, 1fr)',
        },
      }}
    >
      {data.map((project) => (
        <ProjectCard key={project.id} data={project} onClick={() => viewProject(project.id)} />
      ))}
    </Box>
  );
};

export default ProjectsList;
