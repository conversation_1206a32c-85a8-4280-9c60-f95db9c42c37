import type { SxProps } from '@mui/material';

import { useState } from 'react';
import { formatDate } from 'date-fns';

import { Tab, Tabs, Paper, Stack, Divider, TextField, Typography, IconButton } from '@mui/material';

import AiAgentIcon from 'src/assets/icons/ai-agent-icon';

import { Iconify } from 'src/components/iconify';

import SelectionButtons from './SelectionButtons';

interface AidaSuggestionsPanelProps {
  projectId: string;
  sx?: SxProps;
}

const AidaSuggestionsPanel: React.FC<AidaSuggestionsPanelProps> = ({ projectId, sx = {} }) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Paper
      elevation={1}
      sx={{
        overflow: {
          xs: 'auto',
          md: 'hidden',
        },
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '50%',
        },
        ...sx,
      }}
    >
      <>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography sx={{mr: 2}} variant="subtitle2" color="textPrimary">
              Aida
            </Typography>
            <IconButton onClick={() => {}} sx={{ p: 0 }}>
              <Iconify icon="material-symbols:mic-outline" width={20} />
            </IconButton>
            <IconButton onClick={() => {}} sx={{ p: 0 }}>
              <Iconify icon="material-symbols:screen-share-outline" width={20} />
            </IconButton>
          </Stack>
          <Stack direction="row" spacing={1} alignItems="center">
            <SelectionButtons />
            <IconButton onClick={() => {}} sx={{ p: 0 }}>
              <Iconify icon="eva:more-vertical-fill" width={20} />
            </IconButton>
          </Stack>
        </Stack>
        <Stack sx={{ my: 2 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              minHeight: 'auto',
              '& .MuiTabs-indicator': {
                backgroundColor: 'primary.main',
                height: 2,
              },
              '& .MuiTab-root': {
                minHeight: 'auto',
                py: 1,
                px: 1,
                mr: 3,
                textTransform: 'none',
                fontSize: '0.875rem',
                fontWeight: 600,
                color: 'text.secondary',
                '&.Mui-selected': {
                  color: 'text.primary',
                },
              },
            }}
          >
            <Tab label="Suggestions" />
          </Tabs>
          <Divider sx={{ mb: 1 }} />
          <Stack direction="row" spacing={2} alignItems="center">
            {/* TODO: extract as item and render list */}
            <AiAgentIcon sx={{ width: 32, height: 32 }} />
            <Stack justifyContent="space-between" spacing={1} sx={{ flexGrow: 1 }}>
              <SuggestionItem suggestion="What is the status of the project?" />
            </Stack>
          </Stack>
        </Stack>
      </>
    </Paper>
  );
};

const SuggestionItem = ({ suggestion }: { suggestion: string }) => {
  const dateTime = formatDate(new Date(), 'MMM dd, yyyy HH:mm:ss');
  
  return <Stack justifyContent="space-between" spacing={1} sx={{ flexGrow: 1 }}>
  <Typography variant="caption" color="textPrimary">
    Aida
    <Typography variant="caption" color="text.secondary" >
      {' '}
      {dateTime}
    </Typography>
  </Typography>
  <TextField
    variant="outlined"
    size="small"
    fullWidth
    placeholder="I'm listening, talk to me"
    sx={{ flexGrow: 1 }}
  />
</Stack>
}

export default AidaSuggestionsPanel;
