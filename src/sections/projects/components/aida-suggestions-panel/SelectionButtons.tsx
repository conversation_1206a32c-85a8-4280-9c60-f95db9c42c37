import React, { useState } from 'react';

import {
  FlashOn,
  Balance,
  Psychology
} from '@mui/icons-material';
import { 
  Stack, 
  alpha, 
  useTheme,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';

export default function SelectionButtons() {
  const [selected, setSelected] = useState('balanced');
  const theme = useTheme();

  const options = [
    {
      id: 'fast',
      label: 'Fast',
      icon: <FlashOn />,
      color: theme.palette.primary.main,
    },
    {
      id: 'balanced',
      label: 'Balanced',
      icon: <Balance />,
      color: theme.palette.primary.main,
    },
    {
      id: 'deep',
      label: 'Deep',
      icon: <Psychology />,
      color: theme.palette.secondary.main,
    }
  ];

  const handleSelect = (event: React.MouseEvent<HTMLElement>, newSelected: string | null) => {
    if (newSelected !== null) {
      setSelected(newSelected);
    }
  };

  return (

      <ToggleButtonGroup
        value={selected}
        exclusive
        onChange={handleSelect}
        size='small'
        sx={{
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 1,
          p: 0
        }}
      >
        {options.map((option) => (
          <ToggleButton
            key={option.id}
            value={option.id}
            sx={{
              color: selected === option.id ? option.color : 'text.secondary',
              bgcolor: selected === option.id ? alpha(option.color, 0.08) : 'transparent',
              border: selected === option.id ? `2px solid ${alpha(option.color, 0.2)}` : '2px solid transparent',
              '&.Mui-selected': {
                bgcolor: alpha(option.color, 0.08),
                color: option.color,
                border: `2px solid ${alpha(option.color, 0.2)}`,
              },
            }}
          >
            <Stack direction="row" spacing={1} alignItems="center">
              {option.icon}
              <span>{option.label}</span>
            </Stack>
          </ToggleButton>
        ))}
      </ToggleButtonGroup>
  );
}