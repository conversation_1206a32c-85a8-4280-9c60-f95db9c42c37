import type { ProjectDetails } from 'src/types/project';

import { useBoolean } from 'minimal-shared/hooks';

import { Stack, IconButton, Typography } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { CONFIG } from 'src/global-config';

import { Iconify } from 'src/components/iconify';
import { EmptyContent } from 'src/components/empty-content';

import FolderCard from './folder-card';
import CreateFolderDialog from '../create-folder-dialog';

interface Props {
  project: ProjectDetails;
}
const FoldersSection: React.FC<Props> = ({ project }) => {
  const createFolderDialog = useBoolean();
  const router = useRouter();

  return (
    <>
      <Stack direction="column" gap={1}>
        <Stack direction="row" gap={1} alignItems="center">
          <Typography variant="h6">Folders</Typography>

          <IconButton size="small" onClick={createFolderDialog.onTrue}>
            <Iconify icon="fluent:add-circle-12-filled" width={24} sx={{ color: 'primary.main' }} />
          </IconButton>
        </Stack>
        <Stack direction="row" flexWrap="unset" gap={2} className="scrollbar">
          {project?.folders.length > 0 ? (
            <>
              {project?.folders.map((folder) => (
                <FolderCard
                  onClick={() => router.push(paths.project.folder(project.id, folder.id))}
                  key={folder.id}
                  data={folder}
                />
              ))}
            </>
          ) : (
            <EmptyContent
              filled
              title="No folders"
              imgUrl={`${CONFIG.assetsDir}/assets/icons/empty/ic-folder-empty.svg`}
              slotProps={{
                img: {
                  sx: {
                    height: 64,
                  },
                },
              }}
              sx={{ py: 1 }}
            />
          )}
        </Stack>
      </Stack>

      {createFolderDialog.value && (
        <CreateFolderDialog
          project={project}
          open={createFolderDialog.value}
          onClose={createFolderDialog.onFalse}
        />
      )}
    </>
  );
};

export default FoldersSection;
