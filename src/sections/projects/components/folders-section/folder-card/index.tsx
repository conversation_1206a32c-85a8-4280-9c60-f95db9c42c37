import type { PaperProps } from '@mui/material';
import type { ProjectFolder } from 'src/types/project';

import { format } from 'date-fns';
import { useBoolean, usePopover } from 'minimal-shared/hooks';

import LoadingButton from '@mui/lab/LoadingButton';
import {
  Box,
  Paper,
  MenuList,
  MenuItem,
  Typography,
  IconButton,
  ListItemText,
} from '@mui/material';

import { CONFIG } from 'src/global-config';
import { useDeleteProjectFolderMutation } from 'src/store/api/projects';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { CustomPopover } from 'src/components/custom-popover';

const FolderCard: React.FC<{ data: ProjectFolder } & PaperProps> = ({ data, sx, ...other }) => {
  const confirmDialog = useBoolean();
  const menuActions = usePopover();
  const [deleteProjectFolder, { isLoading: isDeleting }] = useDeleteProjectFolderMutation();

  const onDelete = async () => {
    try {
      await deleteProjectFolder({ id: data.projectId, folderId: data.id });
      toast.success('Folder deleted!');
    } catch (error) {
      toast.error('Failed to delete folder');
    }
  };

  const renderIcon = () => (
    <Box sx={{ width: 36, height: 36 }}>
      <Box
        component="img"
        src={`${CONFIG.assetsDir}/assets/icons/files/ic-folder.svg`}
        sx={{ width: 1, height: 1 }}
      />
    </Box>
  );

  const renderText = () => (
    <ListItemText
      primary={data.name}
      primaryTypographyProps={{ noWrap: true, typography: 'subtitle1' }}
    />
  );

  const renderFooter = () => (
    <Typography variant="caption" color="textSecondary">
      Created: {format(data.createdAt, 'dd MMM yyyy')}
    </Typography>
  );

  const renderMenuActions = () => (
    <CustomPopover
      open={menuActions.open}
      anchorEl={menuActions.anchorEl}
      onClose={menuActions.onClose}
      slotProps={{ arrow: { placement: 'left-bottom' } }}
    >
      <MenuList>
        <MenuItem
          onClick={() => {
            confirmDialog.onTrue();
            menuActions.onClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <Iconify icon="solar:trash-bin-trash-bold" />
          Delete
        </MenuItem>
      </MenuList>
    </CustomPopover>
  );

  const renderConfirmDialog = () => (
    <ConfirmDialog
      open={confirmDialog.value}
      onClose={confirmDialog.onFalse}
      title={`Delete ${data.name} folder`}
      content="Are you sure want to delete this folder? This action cannot be undone."
      action={
        <LoadingButton variant="contained" color="error" onClick={onDelete} loading={isDeleting}>
          Delete
        </LoadingButton>
      }
    />
  );

  const renderActions = () => (
    <Box
      sx={{
        top: 8,
        right: 8,
        display: 'flex',
        position: 'absolute',
        alignItems: 'center',
      }}
    >
      <IconButton
        color={menuActions.open ? 'inherit' : 'default'}
        onClick={(evt) => {
          evt.stopPropagation();
          menuActions.onOpen(evt);
        }}
      >
        <Iconify icon="eva:more-vertical-fill" />
      </IconButton>
    </Box>
  );

  return (
    <>
      <Paper
        variant="outlined"
        sx={[
          (theme) => ({
            gap: 1,
            p: 2.5,
            display: 'flex',
            borderRadius: 2,
            cursor: 'pointer',
            position: 'relative',
            bgcolor: 'transparent',
            flexDirection: 'column',
            alignItems: 'flex-start',
            width: 'fit-content',
            minWidth: 200,
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
        {...other}
      >
        {renderIcon()}
        {renderActions()}
        {renderText()}
        {renderFooter()}
      </Paper>

      {renderMenuActions()}
      {renderConfirmDialog()}
    </>
  );
};

export default FolderCard;
