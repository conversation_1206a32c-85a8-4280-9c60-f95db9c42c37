import type { SxProps } from '@mui/material';
import type { Note } from 'src/store/api/notes/types';

import { toast } from 'sonner';
import { useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';

import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { Stack, Paper, IconButton, Typography, CircularProgress } from '@mui/material';

import { useAppSelector } from 'src/store';
import { selectSelectedNote } from 'src/store/slices/notes/selectors';
import { addNote, deleteNote, selectNote, storeNotes } from 'src/store/slices/notes/slice';
import {
  useCreateNoteMutation,
  useDeleteNoteMutation,
  useGetNotesByProjectQuery,
} from 'src/store/api/notes';

import { NoteDialog } from 'src/components/notes';
import { Scrollbar } from 'src/components/scrollbar';
import { NoteList } from 'src/components/notes/note-list';
import { LoadingScreen } from 'src/components/loading-screen';

interface NotesSectionProps {
  projectId: string;
  sx?: SxProps;
}

const NotesPanel: React.FC<NotesSectionProps> = ({ projectId, sx = {} }) => {
  const dispatch = useDispatch();
  const selectedNote = useAppSelector(selectSelectedNote);

  const [createNote, { isLoading: isCreating }] = useCreateNoteMutation();
  const [deleteNoteMutation] = useDeleteNoteMutation();
  const {
    data: apiNotes = [],
    isLoading: isApiLoading,
    refetch,
  } = useGetNotesByProjectQuery(
    { projectId },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  useEffect(() => {
    if (!isApiLoading && apiNotes) {
      dispatch(storeNotes(apiNotes));
    }
  }, [dispatch, isApiLoading, apiNotes]);

  const handleSelectNote = (note: Note) => {
    dispatch(selectNote(note));
  };

  const handleCloseDialog = () => {
    refetch();
    dispatch(selectNote(null));
  };

  const handleAddNewNote = useCallback(() => {
    createNote({
      payload: {
        content: 'Edit this note',
        title: 'New Note',
        projectId,
      },
    })
      .then((res) => {
        if (res.data) {
          handleSelectNote(res.data);
          dispatch(addNote(res.data));
        }
      })
      .catch((err) => {
        toast.error('Failed to add new note');
      });
  }, [createNote, projectId, handleSelectNote]);

  const handleDelete = async (noteId: string) => {
    try {
      await deleteNoteMutation({ id: noteId });
      dispatch(deleteNote(noteId));
      await refetch();
      toast.success('Note deleted successfully');
    } catch (error) {
      toast.error('Failed to delete note');
    }
  };

  return (
    <>
      <Paper
        elevation={1}
        sx={{
          overflow: 'hidden',
          height: ['50%', '100%'],
          display: 'flex',
          flexDirection: 'column',
          p: 2,
          width: {
            xs: '100%',
            md: '25%',
          },
          ...sx,
        }}
      >
        {isApiLoading ? (
          <LoadingScreen />
        ) : (
          <>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="subtitle2" color="textPrimary">
                Notes
              </Typography>
              <IconButton disabled={isCreating} onClick={handleAddNewNote} sx={{ p: 0 }}>
                {isCreating ? <CircularProgress size={20} /> : <AddRoundedIcon />}
              </IconButton>
            </Stack>
            <Scrollbar sx={{ height: '100%', mt: 1 }}>
              <NoteList
                notes={apiNotes}
                onSelectNote={handleSelectNote}
                onDelete={handleDelete}
                onAddNote={handleAddNewNote}
                isCanDeleteResource
              />
            </Scrollbar>
          </>
        )}
      </Paper>

      {/* Note Dialog */}
      <NoteDialog open={Boolean(selectedNote)} onClose={handleCloseDialog} />
    </>
  );
};

export default NotesPanel;
