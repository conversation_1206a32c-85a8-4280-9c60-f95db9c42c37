import type { SxProps } from '@mui/material';

import { useBoolean } from 'minimal-shared/hooks';
import { useMemo, useState, useEffect } from 'react';

import { Stack, Select, Button, MenuItem, InputLabel, FormControl } from '@mui/material';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { Iconify } from 'src/components/iconify';

import CreateProjectDialog from '../create-project-dialog';

type Props = {
  projectId?: string | null;
  onChange: (projectId: string) => void;
  sx?: SxProps;
  disabled?: boolean;
  excludedProjects?: string[];
};

const ProjectDropdown: React.FC<Props> = ({
  projectId,
  onChange,
  sx,
  disabled,
  excludedProjects = [],
}) => {
  const { projects = [], defaultProject } = useUserInitialContext();
  const createProjectDialog = useBoolean();
  const [selectedProjectId, setSelectedProjectId] = useState<string>(() => {
    if (projectId) return projectId;
    return defaultProject?.id || '';
  });

  const filteredProjects = useMemo(
    () => projects.filter((project) => !excludedProjects.includes(project.id)),
    [projects, excludedProjects]
  );

  useEffect(() => {
    onChange(selectedProjectId);
  }, [onChange, selectedProjectId]);

  return (
    <>
      <Stack direction="column" gap={3} sx={{ width: '100%', ...sx }}>
        <FormControl fullWidth size="small">
          <InputLabel htmlFor="project-select" sx={{ zIndex: 2 }}>
            Select project
          </InputLabel>
          <Select
            label="Select project"
            fullWidth
            value={selectedProjectId}
            onChange={(e) => setSelectedProjectId(e.target.value)}
            inputProps={{ id: 'project-select' }}
            readOnly={disabled}
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {filteredProjects.map((project) => (
              <MenuItem key={project.id} value={project.id}>
                {project.name}
              </MenuItem>
            ))}
            <MenuItem onClick={createProjectDialog.onTrue}>
              <Button
                size="small"
                fullWidth
                color="inherit"
                startIcon={<Iconify icon="eva:plus-fill" />}
              >
                Create new project
              </Button>
            </MenuItem>
          </Select>
        </FormControl>
      </Stack>

      {/* Dialogs */}
      <CreateProjectDialog open={createProjectDialog.value} onClose={createProjectDialog.onFalse} />
    </>
  );
};

export default ProjectDropdown;
