import type { Resource } from 'src/types';

import { useBoolean } from 'minimal-shared/hooks';

import { Stack, IconButton, Typography } from '@mui/material';

import { fData } from 'src/utils/format-number';

import { Iconify } from 'src/components/iconify';

import ResourcesList from 'src/sections/resources/components/resources-list';
import { convertResourceToResourceItem } from 'src/sections/resources/utils';
import UploadResourceDialog from 'src/sections/resources/components/upload-resource-dialog';

const FilesSection: React.FC<{
  projectId: string;
  files: Resource[];
  canEdit: boolean;
}> = ({ projectId, files, canEdit }) => {
  const uploadDialog = useBoolean();
  const totalFilesCount = files.length;
  const totalFilesSize = files.reduce((acc, file) => acc + file.fileSize, 0);

  const resources = (files ?? []).map(convertResourceToResourceItem);

  return (
    <>
      <Stack direction="column" gap={1}>
        <Stack direction="column">
          <Stack direction="row" gap={1} alignItems="center">
            <Typography variant="h6">Files</Typography>

            {canEdit && (
              <IconButton size="small" onClick={uploadDialog.onTrue}>
                <Iconify
                  icon="fluent:add-circle-12-filled"
                  width={24}
                  sx={{ color: 'primary.main' }}
                />
              </IconButton>
            )}
          </Stack>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {totalFilesCount} files | {fData(totalFilesSize)}
          </Typography>
        </Stack>
        <ResourcesList items={resources} uploadConfig={{ projectId }} showUploadButton={canEdit} />
      </Stack>

      {canEdit && uploadDialog.value && (
        <UploadResourceDialog
          open={uploadDialog.value}
          onClose={uploadDialog.onFalse}
          projectId={projectId}
        />
      )}
    </>
  );
};

export default FilesSection;
