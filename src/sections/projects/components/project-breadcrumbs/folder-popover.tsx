import type { Theme, SxProps } from '@mui/material/styles';
import type { Project, ProjectFolder } from 'src/types/project';
import type { ButtonBaseProps } from '@mui/material/ButtonBase';

import { useCallback } from 'react';
import { usePopover } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import ButtonBase from '@mui/material/ButtonBase';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { CONFIG } from 'src/global-config';

import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

// ----------------------------------------------------------------------

export type FolderPopoverProps = ButtonBaseProps & {
  data?: Partial<ProjectFolder>[];
  currentFolderId: string;
  currentProject: Pick<Project, 'id' | 'name'>;
};

export function FolderPopover({
  data = [],
  currentFolderId,
  currentProject,
  sx,
  ...other
}: FolderPopoverProps) {
  const router = useRouter();

  const { open, anchorEl, onClose, onOpen } = usePopover();

  const folder = data.find((f) => f.id === currentFolderId);

  const handleChangeFolder = useCallback(
    (newValue: (typeof data)[0]) => {
      router.push(paths.project.folder(currentProject.id, newValue?.id || ''));
      onClose();
    },
    [onClose, router, currentProject.id]
  );

  const buttonBg: SxProps<Theme> = {
    height: 1,
    zIndex: -1,
    opacity: 0,
    content: "''",
    borderRadius: 1,
    position: 'absolute',
    visibility: 'hidden',
    bgcolor: 'action.hover',
    width: 'calc(100% + 8px)',
    transition: (theme) =>
      theme.transitions.create(['opacity', 'visibility'], {
        easing: theme.transitions.easing.sharp,
        duration: theme.transitions.duration.shorter,
      }),
    ...(open && {
      opacity: 1,
      visibility: 'visible',
    }),
  };

  const renderButton = () => (
    <ButtonBase
      disableRipple
      onClick={onOpen}
      sx={[
        {
          p: 1,
          gap: { xs: 0.5, sm: 1 },
          '&::before': buttonBg,
          '&:hover': {
            bgcolor: 'action.hover',
          },
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      <Box sx={{ width: 24, height: 24 }}>
        <Box
          component="img"
          src={`${CONFIG.assetsDir}/assets/icons/files/ic-folder.svg`}
          sx={{ width: 1, height: 1 }}
        />
      </Box>

      <Box
        component="span"
        sx={{ typography: 'subtitle2', display: { xs: 'none', sm: 'inline-flex' } }}
      >
        {folder?.name}
      </Box>

      <Iconify width={16} icon="carbon:chevron-sort" sx={{ color: 'text.disabled' }} />
    </ButtonBase>
  );

  const renderMenuList = () => (
    <CustomPopover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      slotProps={{
        arrow: { placement: 'top-left' },
        paper: { sx: { mt: 0.5, ml: -1.55 } },
      }}
    >
      <MenuList sx={{ width: 240 }}>
        {data.map((option) => (
          <MenuItem
            key={option.id}
            selected={option.id === folder?.id}
            onClick={() => handleChangeFolder(option)}
            sx={{ height: 48 }}
          >
            <Box component="span" sx={{ flexGrow: 1, fontWeight: 'fontWeightMedium' }}>
              {option.name}
            </Box>
          </MenuItem>
        ))}
      </MenuList>
    </CustomPopover>
  );

  return (
    <>
      {renderButton()}
      {renderMenuList()}
    </>
  );
}
