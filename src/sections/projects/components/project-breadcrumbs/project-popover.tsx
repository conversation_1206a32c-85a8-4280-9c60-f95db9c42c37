import type { Theme, SxProps } from '@mui/material/styles';
import type { ButtonBaseProps } from '@mui/material/ButtonBase';
import type { UserProject, UserInitialContext } from 'src/types';

import { useCallback } from 'react';
import { useBoolean, usePopover } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import { Stack, Divider } from '@mui/material';
import ButtonBase from '@mui/material/ButtonBase';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { CONFIG } from 'src/global-config';

import { Iconify } from 'src/components/iconify';
import { SvgColor } from 'src/components/svg-color';
import { CustomPopover } from 'src/components/custom-popover';

import ProjectItemActions from './project-item-actions';
import CreateProjectDialog from '../create-project-dialog';

// ----------------------------------------------------------------------

export type ProjectPopoverProps = ButtonBaseProps & {
  data?: UserInitialContext['projects'];
  currentProject: UserProject;
  showAsLink?: boolean;
};

export function ProjectPopover({
  data = [],
  currentProject: project,
  showAsLink = false,
  sx,
  ...other
}: ProjectPopoverProps) {
  const router = useRouter();

  const { open, anchorEl, onClose, onOpen } = usePopover();
  const createProjectDialog = useBoolean();

  // Check if the current user is the project owner
  const handleChangeProject = useCallback(
    (newValue: (typeof data)[0]) => {
      router.push(paths.project.details(newValue?.id || ''));
      onClose();
    },
    [onClose, router]
  );

  const handleClick = useCallback(
    (evt: React.MouseEvent<HTMLButtonElement>) => {
      evt.stopPropagation();
      if (showAsLink) {
        router.push(paths.project.details(project?.id || ''));
      } else {
        onOpen(evt);
      }
    },
    [onOpen, project?.id, router, showAsLink]
  );

  const buttonBg: SxProps<Theme> = {
    height: 1,
    zIndex: -1,
    opacity: 0,
    content: "''",
    borderRadius: 1,
    position: 'absolute',
    visibility: 'hidden',
    bgcolor: 'action.hover',
    width: 'calc(100% + 8px)',
    transition: (theme) =>
      theme.transitions.create(['opacity', 'visibility'], {
        easing: theme.transitions.easing.sharp,
        duration: theme.transitions.duration.shorter,
      }),
    ...(open && {
      opacity: 1,
      visibility: 'visible',
    }),
  };

  const renderButton = () => (
    <Stack direction="row" alignItems="center" gap={1}>
      <ButtonBase
        onClick={handleClick}
        sx={[
          {
            p: 1,
            gap: { xs: 0.5, sm: 1 },
            '&::before': buttonBg,
            '&:hover': {
              bgcolor: 'action.hover',
            },
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
        {...other}
      >
        <SvgColor
          color="primary"
          src={`${CONFIG.assetsDir}/assets/icons/navbar/ic-folder.svg`}
          sx={{ width: 24, height: 24, color: 'primary.main' }}
        />

        <Box
          component="span"
          sx={{ typography: 'subtitle2', display: { xs: 'none', sm: 'inline-flex' } }}
        >
          {project?.name}
        </Box>

        {!showAsLink && (
          <Iconify width={16} icon="carbon:chevron-sort" sx={{ color: 'text.disabled' }} />
        )}
      </ButtonBase>
    </Stack>
  );

  const renderMenuList = () => (
    <CustomPopover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      slotProps={{
        arrow: { placement: 'top-left' },
        paper: {
          sx: { mt: 0.5, ml: -1.55 },
          onClick: (evt) => evt.stopPropagation(),
        },
      }}
    >
      <MenuList sx={{ width: 240 }}>
        {data.map((option) => (
          <MenuItem
            key={option.id}
            selected={option.id === project?.id}
            onClick={(evt) => {
              evt.stopPropagation();
              handleChangeProject(option);
            }}
            sx={{ height: 48 }}
          >
            <Box component="span" sx={{ flexGrow: 1, fontWeight: 'fontWeightMedium' }}>
              {option.name}
            </Box>
            <ProjectItemActions data={option} />
          </MenuItem>
        ))}
      </MenuList>
      <Divider sx={{ my: 1 }} />
      <MenuItem
        onClick={(evt) => {
          evt.stopPropagation();
          createProjectDialog.onTrue();
        }}
      >
        <ButtonBase sx={{ fontWeight: 600 }}>
          <Iconify icon="carbon:add" />
          Create new project
        </ButtonBase>
      </MenuItem>
    </CustomPopover>
  );

  return (
    <>
      {renderButton()}
      {renderMenuList()}

      <CreateProjectDialog open={createProjectDialog.value} onClose={createProjectDialog.onFalse} />
    </>
  );
}
