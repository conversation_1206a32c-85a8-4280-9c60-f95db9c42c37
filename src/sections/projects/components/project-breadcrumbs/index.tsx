import { useBoolean } from 'minimal-shared/hooks';
import { isActiveLink } from 'minimal-shared/utils';

import { Stack } from '@mui/material';
import NavigateNextRoundedIcon from '@mui/icons-material/NavigateNextRounded';

import { paths } from 'src/routes/paths';
import { useParams, usePathname } from 'src/routes/hooks';

import useUserInitialContext from 'src/hooks/user-initial-context';

import ChatDialog from 'src/sections/chat/chat-dialog';

import { FolderPopover } from './folder-popover';
import { ProjectPopover } from './project-popover';

const ProjectBreadcrumbs: React.FC = () => {
  const pathname = usePathname();
  const { id: projectId = '', folderId } = useParams();
  const { projects } = useUserInitialContext();

  const openChatDialog = useBoolean();
  const showBreadcrumbs = isActiveLink(pathname, paths.project.details(projectId));
  const currentProject = projects.find((project) => project.id === projectId);
  const currentFolder = currentProject?.folders.find((folder) => folder.id === folderId);

  if (!showBreadcrumbs || !currentProject) return null;

  return (
    <>
      <Stack direction="row" alignItems="center">
        <ProjectPopover
          showAsLink={!!currentFolder}
          data={projects}
          currentProject={currentProject}
        />
        {currentFolder && (
          <>
            <NavigateNextRoundedIcon fontSize="medium" color="action" />
            <FolderPopover
              data={currentProject.folders}
              currentFolderId={currentFolder.id}
              currentProject={currentProject}
            />
          </>
        )}
      </Stack>

      {openChatDialog.value && (
        <ChatDialog
          open={openChatDialog.value}
          onClose={openChatDialog.onFalse}
          projectId={currentProject.id}
          key={`chat-dialog-${currentProject.id}`}
          initialMessages={['Summarise']}
        />
      )}
    </>
  );
};

export default ProjectBreadcrumbs;
