import { z as zod } from 'zod';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';

import LoadingButton from '@mui/lab/LoadingButton';
import {
  Stack,
  Dialog,
  Typography,
  DialogTitle,
  DialogContent,
  InputAdornment,
} from '@mui/material';

import useAnalytics from 'src/hooks/analytics';

import { withHttps } from 'src/utils/url';
import { isValidMeetingUrl, MEETING_URL_FORMATS } from 'src/utils/regex';

import { useRecordSessionMutation } from 'src/store/api/sessions';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

interface Props {
  open: boolean;
  onClose: () => void;
  projectId: string;
}

interface FormValues {
  meetingUrl: string;
}

const StartRecordingDialog: React.FC<Props> = ({ open, onClose, projectId }) => {
  const { trackEvent } = useAnalytics();
  const [currentFormatIndex, setCurrentFormatIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFormatIndex(
        (prevIndex) => (prevIndex + 1) % Object.values(MEETING_URL_FORMATS).length
      );
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const methods = useForm<FormValues>({
    mode: 'onChange',
    resolver: zodResolver(
      zod.object({
        meetingUrl: zod
          .string()
          .min(1, { message: 'Meeting URL is required' })
          .refine(
            (url) => {
              const { isValid } = isValidMeetingUrl(withHttps(url));
              return isValid;
            },
            { message: 'Invalid meeting URL' }
          ),
      })
    ),
    defaultValues: {
      meetingUrl: '',
    },
  });

  const {
    handleSubmit,
    formState: { errors },
    reset,
  } = methods;

  const [triggerRecording, { isLoading }] = useRecordSessionMutation();

  const onSubmit = handleSubmit(async (data) => {
    try {
      await triggerRecording({
        payload: { url: withHttps(data.meetingUrl), projectId },
      }).unwrap();

      trackEvent({
        eventCategory: 'Session',
        eventAction: 'Started meeting recording',
        properties: {
          url: data.meetingUrl,
          projectId,
        },
      });

      toast.success('Recording started successfully', {
        description:
          'Aida bot will request to join your meeting in a few seconds. Please admit to let it join',
      });

      reset({ meetingUrl: '' });
      onClose();
    } catch (error) {
      toast.error('Failed to start recording', {
        description: 'Something went wrong. Please try again.',
      });
    }
  });

  const currentFormat = Object.values(MEETING_URL_FORMATS)[currentFormatIndex];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Typography variant="h6">Start Recording</Typography>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify icon="logos:google-meet" />
            <Iconify icon="logos:zoom-icon" />
            <Iconify icon="logos:microsoft-teams" />
          </Stack>
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ pb: 4 }}>
        <Stack spacing={2}>
          <Typography variant="body2">
            Please admit <strong><EMAIL></strong> when they ask to join your meeting.
          </Typography>

          <Form methods={methods} onSubmit={onSubmit}>
            <Stack spacing={1.5}>
              <Field.Text
                slotProps={{
                  input: {
                    startAdornment: <InputAdornment position="start">https://</InputAdornment>,
                  },
                }}
                name="meetingUrl"
                label="Meeting URL"
                placeholder={currentFormat}
                type="text"
                variant="outlined"
                fullWidth
                error={!!errors.meetingUrl}
                helperText={errors.meetingUrl?.message}
              />

              <LoadingButton
                type="submit"
                variant="contained"
                color="error"
                loading={isLoading}
                startIcon={<Iconify icon="solar:record-bold-duotone" />}
                fullWidth
                size="medium"
              >
                Start Recording
              </LoadingButton>
            </Stack>
          </Form>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

export default StartRecordingDialog;
