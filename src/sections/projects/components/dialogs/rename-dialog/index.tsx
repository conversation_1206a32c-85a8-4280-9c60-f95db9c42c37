import type { Project } from 'src/types/project';

import { z as zod } from 'zod';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useUpdateProjectMutation } from 'src/store/api/projects';

import { Form, Field } from 'src/components/hook-form';

interface Props {
  open: boolean;
  onClose: () => void;
  project: Pick<Project, 'id' | 'name'>;
}

interface FormValues {
  name: string;
}

const RenameProjectDialog: React.FC<Props> = ({ open, onClose, project }) => {
  const [updateProject, { isLoading: isUpdatingProject }] = useUpdateProjectMutation();

  const methods = useForm<FormValues>({
    defaultValues: {
      name: project.name,
    },
    resolver: zodResolver(
      zod.object({
        name: zod.string().min(1, { message: 'Please input project name' }),
      })
    ),
  });

  const {
    handleSubmit,
    formState: { errors, isValid },
  } = methods;

  const onSubmit = async (data: FormValues) => {
    try {
      await updateProject({
        id: project.id,
        payload: {
          name: data.name,
        },
      }).unwrap();

      onClose();
      toast.success('Project name updated successfully');
    } catch (error) {
      toast.error('Failed to update project name');
    }
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      onClick={(evt) => evt.stopPropagation()}
    >
      <DialogTitle sx={[(theme) => ({ p: theme.spacing(3, 3, 2, 3) })]}>Rename Project</DialogTitle>
      <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers sx={{ pt: 1, pb: 0, border: 'none' }}>
          <Field.Text name="name" label="Project name" fullWidth error={!!errors.name} />
        </DialogContent>
        <DialogActions>
          <LoadingButton
            disabled={!isValid}
            variant="contained"
            type="submit"
            loading={isUpdatingProject}
          >
            Update
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
};

export default RenameProjectDialog;
