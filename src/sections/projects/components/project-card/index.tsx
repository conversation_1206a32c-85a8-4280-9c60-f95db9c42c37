import type { PaperProps } from '@mui/material';
import type { Project } from 'src/types/project';

import { toast } from 'sonner';
import { useMemo } from 'react';
import { format } from 'date-fns';
import { useBoolean, usePopover } from 'minimal-shared/hooks';

import LoadingButton from '@mui/lab/LoadingButton';
import AutoAwesomeRoundedIcon from '@mui/icons-material/AutoAwesomeRounded';
import DeleteForeverRoundedIcon from '@mui/icons-material/DeleteForeverRounded';
import {
  Box,
  Paper,
  MenuItem,
  MenuList,
  IconButton,
  Typography,
  ListItemText,
} from '@mui/material';

import useUserInitialContext from 'src/hooks/user-initial-context';
import useResourcePermissions from 'src/hooks/use-resource-permissions';

import { fData } from 'src/utils/format-number';

import { AUTH } from 'src/lib/firebase';
import { CONFIG } from 'src/global-config';
import { useDeleteProjectMutation } from 'src/store/api/projects';

import { Iconify } from 'src/components/iconify';
import { SvgColor } from 'src/components/svg-color';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { CustomPopover } from 'src/components/custom-popover';

import ChatDialog from 'src/sections/chat/chat-dialog';

import LeaveProjectDialog from '../dialogs/leave-dialog';

const ProjectCard: React.FC<{ data: Project; hideActions?: boolean } & PaperProps> = ({
  data,
  hideActions,
  sx,
  ...other
}) => {
  const confirmDialog = useBoolean();
  const leaveProjectDialog = useBoolean();
  const openChatDialog = useBoolean();
  const menuActions = usePopover();

  const [triggerDeleteProject, { isLoading }] = useDeleteProjectMutation();
  const { canEdit } = useResourcePermissions({ resource: data });
  const { defaultProject } = useUserInitialContext();

  const userId = AUTH.currentUser?.uid ?? 'user';
  const isOwner = data.createdById === userId;
  const isDefault = useMemo(() => defaultProject?.id === data.id, [defaultProject, data.id]);

  const onDelete = async () => {
    try {
      await triggerDeleteProject({ id: data.id }).unwrap();
      confirmDialog.onFalse();
      toast.success('Project deleted!');
    } catch {
      toast.error('Failed to delete project');
    }
  };

  const renderIcon = () => (
    <SvgColor
      color="primary"
      src={`${CONFIG.assetsDir}/assets/icons/navbar/ic-folder.svg`}
      sx={{ width: 40, height: 40, color: 'primary.main' }}
    />
  );

  const renderText = () => (
    <ListItemText
      primary={data.name}
      secondary={
        <>
          {fData(data.totalFilesSize)}
          <Box
            component="span"
            sx={{
              mx: 0.75,
              width: 2,
              height: 2,
              borderRadius: '50%',
              bgcolor: 'currentColor',
            }}
          />
          {data.totalFilesCount} files
        </>
      }
      primaryTypographyProps={{ noWrap: true, typography: 'subtitle1' }}
      secondaryTypographyProps={{
        mt: 0.5,
        component: 'span',
        alignItems: 'center',
        typography: 'caption',
        color: 'text.disabled',
        display: 'inline-flex',
      }}
    />
  );

  const renderFooter = () => (
    <Typography variant="caption" color="textSecondary" sx={{ mt: 0.5 }}>
      Created: {format(data.createdAt, 'dd MMM yyyy')}
    </Typography>
  );

  const renderMenuActions = () => (
    <CustomPopover
      open={menuActions.open}
      anchorEl={menuActions.anchorEl}
      onClose={menuActions.onClose}
      slotProps={{ arrow: { placement: 'left-bottom' } }}
    >
      <MenuList>
        <MenuItem onClick={openChatDialog.onTrue}>
          <AutoAwesomeRoundedIcon fontSize="small" />
          Summarise this project
        </MenuItem>
        {!isDefault && (
          <>
            {isOwner ? (
              <MenuItem
                onClick={() => {
                  confirmDialog.onTrue();
                  menuActions.onClose();
                }}
                sx={{
                  color: canEdit ? 'error.main' : 'inherit',
                }}
                disabled={!canEdit}
              >
                <DeleteForeverRoundedIcon fontSize="small" />
                Delete
              </MenuItem>
            ) : (
              <MenuItem
                onClick={() => {
                  leaveProjectDialog.onTrue();
                  menuActions.onClose();
                }}
                sx={{ color: 'inherit' }}
              >
                <Iconify icon="solar:logout-2-bold" />
                Leave Project
              </MenuItem>
            )}
          </>
        )}
      </MenuList>
    </CustomPopover>
  );

  const renderConfirmDialog = () => (
    <ConfirmDialog
      open={confirmDialog.value}
      onClose={confirmDialog.onFalse}
      title="Delete Project"
      content="Are you sure want to delete this project? This action cannot be undone."
      action={
        <LoadingButton
          variant="contained"
          color="error"
          onClick={onDelete}
          loading={isLoading}
          disabled={isLoading || isDefault}
        >
          Delete
        </LoadingButton>
      }
    />
  );

  const renderActions = () => (
    <Box
      sx={{
        top: 8,
        right: 8,
        display: 'flex',
        position: 'absolute',
        alignItems: 'center',
      }}
    >
      <IconButton
        color={menuActions.open ? 'inherit' : 'default'}
        onClick={(evt) => {
          evt.stopPropagation();
          menuActions.onOpen(evt);
        }}
      >
        <Iconify icon="eva:more-vertical-fill" />
      </IconButton>
    </Box>
  );

  return (
    <>
      <Paper
        variant="outlined"
        sx={[
          (theme) => ({
            gap: 1,
            p: 2.5,
            display: 'flex',
            borderRadius: 2,
            cursor: 'pointer',
            position: 'relative',
            bgcolor: 'transparent',
            flexDirection: 'column',
            alignItems: 'flex-start',
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
        {...other}
      >
        {renderIcon()}
        {!hideActions && renderActions()}
        {renderText()}
        {renderFooter()}
      </Paper>

      {renderMenuActions()}
      {renderConfirmDialog()}

      <LeaveProjectDialog
        open={leaveProjectDialog.value}
        onClose={leaveProjectDialog.onFalse}
        project={data}
      />

      {openChatDialog.value && (
        <ChatDialog
          open={openChatDialog.value}
          onClose={openChatDialog.onFalse}
          projectId={data.id}
          key={`chat-dialog-${data.id}`}
          initialMessages={['Summarise']}
        />
      )}
    </>
  );
};

export default ProjectCard;
