import type { DialogProps } from '@mui/material/Dialog';
import type { ProjectDetails } from 'src/types/project';

import { z as zod } from 'zod';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useCreateProjectFolderMutation } from 'src/store/api/projects';

import { Form, Field } from 'src/components/hook-form';

// ----------------------------------------------------------------------

type Props = DialogProps & {
  open: boolean;
  onClose: () => void;
  project: Pick<ProjectDetails, 'id' | 'name'>;
};

interface FormValues {
  name: string;
}

const CreateFolderDialog: React.FC<Props> = ({ open, onClose, project, ...other }) => {
  const [createFolder, { isLoading }] = useCreateProjectFolderMutation();
  const methods = useForm<FormValues>({
    defaultValues: {
      name: '',
    },
    resolver: zodResolver(
      zod.object({
        name: zod.string().min(1, { message: 'Please input folder name' }),
      })
    ),
  });

  const {
    handleSubmit,
    formState: { errors, isValid },
  } = methods;

  const onSubmit = async (data: FormValues) => {
    try {
      await createFolder({
        id: project.id,
        payload: {
          name: data.name,
        },
      }).unwrap();

      toast.success('Folder created successfully');
      onClose();
    } catch (error) {
      toast.error('Failed to create folder');
    }
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...other}>
      <DialogTitle sx={[(theme) => ({ p: theme.spacing(3, 3, 2, 3) })]}>
        New folder for {project.name}
      </DialogTitle>
      <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers sx={{ pt: 1, pb: 0, border: 'none' }}>
          <Field.Text name="name" label="Folder name" fullWidth error={!!errors.name} />
        </DialogContent>
        <DialogActions>
          <LoadingButton disabled={!isValid} variant="contained" type="submit" loading={isLoading}>
            Create Folder
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
};

export default CreateFolderDialog;
