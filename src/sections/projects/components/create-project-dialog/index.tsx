import type { DialogProps } from '@mui/material/Dialog';

import { z as zod } from 'zod';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useCreateProjectMutation } from 'src/store/api/projects';

import { Form, Field } from 'src/components/hook-form';

// ----------------------------------------------------------------------

type Props = DialogProps & {
  open: boolean;
  onClose: () => void;
};

interface FormValues {
  name: string;
}

const CreateProjectDialog: React.FC<Props> = ({ open, onClose, ...other }) => {
  const [createProject, { isLoading }] = useCreateProjectMutation();
  const methods = useForm<FormValues>({
    defaultValues: {
      name: '',
    },
    resolver: zodResolver(
      zod.object({
        name: zod.string().min(1, { message: 'Please input project name' }),
      })
    ),
  });

  const {
    handleSubmit,
    formState: { errors, isValid },
  } = methods;

  const onSubmit = async (data: FormValues) => {
    try {
      await createProject({
        payload: {
          name: data.name,
        },
      }).unwrap();

      toast.success('Project created successfully');
      onClose();
    } catch (error) {
      toast.error('Failed to create project');
    }
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...other}>
      <DialogTitle sx={[(theme) => ({ p: theme.spacing(3, 3, 2, 3) })]}>New Project</DialogTitle>
      <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers sx={{ pt: 1, pb: 0, border: 'none' }}>
          <Field.Text name="name" label="Project name" fullWidth error={!!errors.name} />
        </DialogContent>
        <DialogActions>
          <LoadingButton disabled={!isValid} variant="contained" type="submit" loading={isLoading}>
            Create Project
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
};

export default CreateProjectDialog;
