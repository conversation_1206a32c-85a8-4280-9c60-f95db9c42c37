import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';

import { useSelector } from 'react-redux';
import { useMemo, useCallback } from 'react';

import { List, Paper, Stack, Checkbox, Typography } from '@mui/material';

import useUserSessions from 'src/hooks/user-sessions';

import { useGetProjectDetailsQuery } from 'src/store/api/projects';
import { selectActiveResourceUploadsForProject } from 'src/store/slices/resources/selectors';

import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content';
import { LoadingScreen } from 'src/components/loading-screen';

import FileCard from './file-card';
import SessionCard from './session-card';
import PendingUploadCard from './pending-upload-card';

interface FilesPanelProps {
  selectedFiles: Resource[];
  onFilesSelected?: (files: Resource[]) => void;
  projectId: string;
  sx?: SxProps;
}

const FilesPanel = ({ selectedFiles, onFilesSelected, projectId, sx = {} }: FilesPanelProps) => {
  const { ongoingSessions = [] } = useUserSessions(projectId);

  const {
    data: project,
    isLoading,
    isFetching,
  } = useGetProjectDetailsQuery(
    { id: projectId },
    {
      skip: !projectId,
      refetchOnFocus: false,
    }
  );

  const pendingUploads = useSelector((state) =>
    selectActiveResourceUploadsForProject(state, projectId)
  );

  // Memoize expensive computations
  const { resources, sortedResources, selectedFileIds, isEmpty, isAllSelected, isIndeterminate } =
    useMemo(() => {
      const projectResources = project?.resources ?? [];
      const sortedProjectResources = [...projectResources].sort(
        (a, b) => new Date(b.fileLastModified).getTime() - new Date(a.fileLastModified).getTime()
      );

      const fileIdSet = new Set(selectedFiles.map((f) => f.id));

      const hasNoItems =
        sortedProjectResources.length === 0 &&
        ongoingSessions.length === 0 &&
        pendingUploads.length === 0;

      const allSelected =
        selectedFiles.length === projectResources.length && projectResources.length > 0;
      const partiallySelected =
        selectedFiles.length > 0 && selectedFiles.length < projectResources.length;

      return {
        resources: projectResources,
        sortedResources: sortedProjectResources,
        selectedFileIds: fileIdSet,
        isEmpty: hasNoItems,
        isAllSelected: allSelected,
        isIndeterminate: partiallySelected,
      };
    }, [project?.resources, selectedFiles, ongoingSessions.length, pendingUploads.length]);

  // Memoize event handlers
  const handleSelectFile = useCallback(
    (file: Resource, isSelected: boolean) => {
      if (!onFilesSelected) return;

      if (isSelected) {
        onFilesSelected([...selectedFiles, file]);
      } else {
        onFilesSelected(selectedFiles.filter((f) => f.id !== file.id));
      }
    },
    [selectedFiles, onFilesSelected]
  );

  const handleSelectAll = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (!onFilesSelected) return;

      if (event.target.checked) {
        onFilesSelected(resources);
      } else {
        onFilesSelected([]);
      }
    },
    [resources, onFilesSelected]
  );

  // Early return for loading state
  if (isLoading || isFetching || !project) {
    return (
      <Paper
        elevation={1}
        sx={{
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          p: 2,
          width: {
            xs: '100%',
            md: '25%',
          },
          height: ['50%', '100%'],
          ...sx,
        }}
      >
        <LoadingScreen />
      </Paper>
    );
  }

  return (
    <Paper
      elevation={1}
      sx={{
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '25%',
        },
        height: ['50%', '100%'],
        ...sx,
      }}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Typography variant="subtitle2">Total Files: {resources.length}</Typography>
        <Checkbox
          size="small"
          checked={isAllSelected}
          indeterminate={isIndeterminate}
          onChange={handleSelectAll}
        />
      </Stack>

      <Scrollbar
        sx={{
          height: '100%',
          width: '100%',
        }}
      >
        <List
          dense
          sx={{
            flex: 1,
            overflowY: 'auto',
            height: '100%',
            gap: 1,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {isEmpty ? (
            <EmptyContent
              title="No files available"
              description="Upload file or start recording to get started"
              sx={{
                height: '100%',
              }}
            />
          ) : (
            <>
              {/* Pending Uploads */}
              {pendingUploads.map((upload) => (
                <PendingUploadCard key={upload.id} data={upload} />
              ))}

              {/* On-going Sessions */}
              {ongoingSessions.map((session) => (
                <SessionCard key={session.id} session={session} />
              ))}

              {/* Files */}
              {sortedResources.map((file) => (
                <FileCard
                  key={file.id}
                  file={file}
                  onSelect={handleSelectFile}
                  isSelected={selectedFileIds.has(file.id)}
                />
              ))}
            </>
          )}
        </List>
      </Scrollbar>
    </Paper>
  );
};

export default FilesPanel;
