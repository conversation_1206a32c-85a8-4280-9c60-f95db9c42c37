import type { Session } from 'src/types';
import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { useBoolean, usePopover } from 'minimal-shared/hooks';

import { Box, Tooltip, MenuItem, MenuList, IconButton } from '@mui/material';

import useResourcePermissions from 'src/hooks/use-resource-permissions';

import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

import RemoveResourceDialog from 'src/sections/resources/components/resource-actions/components/remove-dialog';

interface SessionActionsProps {
  session: Session;
}

const SessionActions = ({ session }: SessionActionsProps) => {
  const deleteDialog = useBoolean();
  const menuActions = usePopover();

  // Convert session to ResourceItem format
  const resourceItem: ResourceItem = {
    ...session,
    cardType: 'session' as const,
    ffprobe: null,
    duration: 0,
    thumbnailUrl: '',
    fileSize: 0,
    fileName: session.title,
    name: session.title,
    url: session.meetingUrl,
    fileLastModified: session.createdAt,
    userPermissions: {
      canView: true,
      canEdit: true,
    },
  };

  const { canEdit } = useResourcePermissions({ resource: resourceItem });

  const onOpenMenu = (evt: React.MouseEvent<HTMLButtonElement>) => {
    evt.stopPropagation();
    menuActions.onOpen(evt);
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <Tooltip title="More actions" placement="top" arrow>
          <IconButton disableRipple size="small" onClick={onOpenMenu}>
            <Iconify icon="eva:more-vertical-fill" />
          </IconButton>
        </Tooltip>
      </Box>

      <CustomPopover
        open={menuActions.open}
        anchorEl={menuActions.anchorEl}
        onClose={menuActions.onClose}
        slotProps={{ arrow: { placement: 'top-left' } }}
      >
        <MenuList>
          {session.meetingUrl && (
            <MenuItem
              component="a"
              href={session.meetingUrl}
              target="_blank"
              rel="noopener noreferrer"
              onClick={menuActions.onClose}
            >
              <Iconify icon="eva:external-link-fill" />
              Join Meeting
            </MenuItem>
          )}
          <MenuItem
            onClick={deleteDialog.onTrue}
            sx={{
              color: 'error.main',
              ...(!canEdit && {
                color: '#fff',
              }),
            }}
            disabled={!canEdit}
          >
            <Iconify icon="solar:trash-bin-trash-bold" />
            Delete
          </MenuItem>
        </MenuList>
      </CustomPopover>

      <RemoveResourceDialog
        isSessionCard
        open={deleteDialog.value}
        onClose={deleteDialog.onFalse}
        resource={resourceItem}
      />
    </>
  );
};

export default SessionActions;
