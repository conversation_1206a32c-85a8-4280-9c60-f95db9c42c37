import type { Session } from 'src/types';

import VideocamIcon from '@mui/icons-material/Videocam';
import { Card, Stack, Typography } from '@mui/material';

import TruncateTypography from 'src/components/truncate-typography';

import { getSessionStatusColor, getSessionStatusDisplay } from 'src/sections/recordings/utils';

import SessionActions from './session-actions';

interface SessionCardProps {
  session: Session;
}

const SessionCard = ({ session }: SessionCardProps) => {
  const statusDisplay = getSessionStatusDisplay(session);
  const statusColor = getSessionStatusColor(session);

  return (
    <Card
      sx={{
        width: '100%',
        bgcolor: 'background.paper',
        borderRadius: 1,
        border: '1px solid',
        borderColor: 'divider',
        boxShadow: 'none',
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        sx={{
          p: 1,
          height: '100%',
        }}
      >
        <VideocamIcon color="action" sx={{ flexShrink: 0 }} />
        <Stack sx={{ flexGrow: 1, minWidth: 0 }}>
          <TruncateTypography
            text={session.title}
            variant="subtitle1"
            fontSize={14}
            sx={{
              whiteSpace: 'nowrap',
            }}
          />
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="caption" color="text.secondary">
              Status:
            </Typography>
            <Typography variant="caption" color={statusColor} fontWeight={600}>
              {statusDisplay}
            </Typography>
          </Stack>
        </Stack>

        <Stack direction="row" alignItems="center" sx={{ flexShrink: 0 }}>
          <SessionActions session={session} />
        </Stack>
      </Stack>
    </Card>
  );
};

export default SessionCard;
