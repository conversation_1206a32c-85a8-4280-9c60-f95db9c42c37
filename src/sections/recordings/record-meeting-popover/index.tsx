/* eslint-disable perfectionist/sort-imports */
import type { Session } from 'src/types';
import type { CustomPopoverProps } from 'src/components/custom-popover';

import React, { useState, useEffect } from 'react';
import { z as zod } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import LoadingButton from '@mui/lab/LoadingButton';
import { Box, Stack, MenuItem, Typography, ListItemText, InputAdornment } from '@mui/material';

import useAnalytics from 'src/hooks/analytics';

import { withHttps } from 'src/utils/url';
import { URL_REGEX, isValidMeetingUrl, MEETING_URL_FORMATS } from 'src/utils/regex';

import { useRecordSessionMutation } from 'src/store/api/sessions';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { Form, Field } from 'src/components/hook-form';
import { iconUrl } from 'src/components/file-thumbnail';
import { CustomPopover } from 'src/components/custom-popover';
import TruncateTypography from 'src/components/truncate-typography';

import { getSessionStatusDisplay } from '../utils';

interface FormValues {
  meetingUrl: string;
}

const RecordingItem: React.FC<{ data: Session }> = ({ data }) => {
  const { title, meetingUrl } = data;

  return (
    <MenuItem>
      <Box component="img" src={iconUrl('ic-video')} />

      <ListItemText
        primary={title}
        secondary={
          <>
            <TruncateTypography
              variant="subtitle1"
              sx={{ fontSize: 12 }}
              text={meetingUrl}
              maxLength={25}
            />
            <Box
              sx={{
                mx: 0.75,
                width: 2,
                height: 2,
                borderRadius: '50%',
                bgcolor: 'currentColor',
              }}
            />
            {getSessionStatusDisplay(data)}
          </>
        }
        primaryTypographyProps={{ noWrap: true, typography: 'subtitle2' }}
        secondaryTypographyProps={{
          mt: 0.5,
          component: 'span',
          alignItems: 'center',
          typography: 'caption',
          color: 'text.disabled',
          display: 'inline-flex',
        }}
      />
    </MenuItem>
  );
};

const RecordMeetingPopover: React.FC<CustomPopoverProps & { sessions: Session[] }> = ({
  sessions,
  ...props
}) => {
  const { trackEvent } = useAnalytics();
  const [currentFormatIndex, setCurrentFormatIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFormatIndex((prevIndex) => 
        (prevIndex + 1) % Object.values(MEETING_URL_FORMATS).length
      );
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const methods = useForm<FormValues>({
    mode: 'onChange',
    resolver: zodResolver(
      zod.object({
        meetingUrl: zod
          .string()
          .min(1, { message: 'Meeting URL is required' })
          .refine((url) => {
            const { isValid } = isValidMeetingUrl(withHttps(url));
            return isValid;
          }, { message: 'Invalid meeting URL' }),
      })
    ),
    defaultValues: {
      meetingUrl: '',
    },
  });

  const {
    handleSubmit,
    formState: { errors },
    reset,
  } = methods;

  const [triggerRecording, { isLoading }] = useRecordSessionMutation();

  const onSubmit = handleSubmit(async (data) => {
    try {
      await triggerRecording({ payload: { url: withHttps(data.meetingUrl) } }).unwrap();

      trackEvent({
        eventCategory: 'Session',
        eventAction: 'Started meeting recording',
        properties: {
          url: data.meetingUrl,
        },
      });

      toast.success('Recording started successfully', {
        description:
          'Aida bot will request to join your meeting in a few seconds. Please admit to let it join',
      });

      reset({ meetingUrl: '' });
    } catch (error) {
      toast.error('Failed to start recording', {
        description: 'Something went wrong. Please try again.',
      });
    }
  });

  const currentFormat = Object.values(MEETING_URL_FORMATS)[currentFormatIndex];

  return (
    <CustomPopover {...props} slotProps={{ arrow: { offset: 20 }, paper: { sx: { p: 2 } } }}>
      <Stack direction="row" alignItems="center" sx={{ mb: 2, gap: 2 }}>
        <Typography variant="h6">Add to live meeting</Typography>
        <Stack direction="row" alignItems="center" gap={1}>
          <Iconify icon="logos:google-meet" />
          <Iconify icon="logos:zoom-icon" />
          <Iconify icon="logos:microsoft-teams" />
        </Stack>
      </Stack>
      <Typography variant="body2" sx={{ mb: 2 }}>
        Please admit <strong><EMAIL></strong> when they ask to join your meeting.
      </Typography>

      <Form methods={methods} onSubmit={onSubmit}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
          <Field.Text
            slotProps={{
              input: {
                startAdornment: <InputAdornment position="start">https://</InputAdornment>,
              },
            }}
            name="meetingUrl"
            label="Meeting URL"
            placeholder={currentFormat}
            type="text"
            variant="outlined"
            size="small"
            sx={{
              fontSize: 12,
            }}
            error={!!errors.meetingUrl}
            helperText={errors.meetingUrl?.message}
          />
          <LoadingButton
            type="submit"
            variant="contained"
            color="error"
            loading={isLoading}
            startIcon={<Iconify icon="solar:record-bold-duotone" />}
            sx={{ flexShrink: 0 }}
          >
            Record
          </LoadingButton>
        </Box>
      </Form>

      <Scrollbar sx={{ height: 'auto', maxHeight: 400, width: '100%', mt: 1 }}>
        {sessions.map((item) => (
          <RecordingItem data={item} key={item.id} />
        ))}
      </Scrollbar>
    </CustomPopover>
  );
};

export default RecordMeetingPopover;
