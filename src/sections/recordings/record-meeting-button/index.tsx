import { m } from 'framer-motion';
import { usePopover } from 'minimal-shared/hooks';

import { Badge, Tooltip, IconButton } from '@mui/material';
import RadioButtonCheckedIcon from '@mui/icons-material/RadioButtonChecked';

import useUserSessions from 'src/hooks/user-sessions';

import { varTap, varHover, transitionTap } from 'src/components/animate';

import RecordMeetingPopover from '../record-meeting-popover';

const RecordMeetingButton: React.FC = () => {
  const { open, anchorEl, onClose, onOpen } = usePopover();

  const { ongoingSessions = [] } = useUserSessions();

  const isRecording = ongoingSessions.length > 0;

  return (
    <>
      <Tooltip title="Recordings" arrow>
        <IconButton
          component={m.button}
          whileTap={varTap(0.96)}
          whileHover={varHover(1.04)}
          transition={transitionTap()}
          aria-label="Record button"
          onClick={onOpen}
        >
          <Badge color="error" variant="dot" invisible={!isRecording}>
            <RadioButtonCheckedIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      <RecordMeetingPopover
        open={open}
        anchorEl={anchorEl}
        onClose={onClose}
        sessions={ongoingSessions}
      />
    </>
  );
};

export default RecordMeetingButton;
