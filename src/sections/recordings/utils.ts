import type { Session } from 'src/types';

import { SessionStatus, RecallBotStatus } from 'src/types';

export const getSessionStatusDisplay = (session: Session) => {
  const { status, recallBotStatus } = session;

  switch (recallBotStatus) {
    case RecallBotStatus.JoiningCall:
      return 'Joining call...';
    case RecallBotStatus.InWaitingRoom:
      return 'Aid<PERSON> is in waiting room...';
    case RecallBotStatus.InCallNotRecording:
      return 'Aida is in the call...';
    case RecallBotStatus.InCallRecording:
      return 'Recording...';
    case RecallBotStatus.CallEnded:
      return 'Call ended';
    case RecallBotStatus.Fatal:
      return 'Error';
    case RecallBotStatus.Done:
      if (status === SessionStatus.Processing) {
        return 'Processing...';
      }
      if (status === SessionStatus.Failed) {
        return 'Failed to process';
      }
      return 'Completed';
    case RecallBotStatus.TimeoutExceededWaitingRoom:
      return 'Aid<PERSON> was not admitted to session. Access denied.';
    default:
      return 'Not started';
  }
};

export const getSessionStatusColor = (session: Session) => {
  const { recallBotStatus } = session;

  switch (recallBotStatus) {
    case RecallBotStatus.TimeoutExceededWaitingRoom:
      return 'error';
    default:
      return 'info';
  }
};
