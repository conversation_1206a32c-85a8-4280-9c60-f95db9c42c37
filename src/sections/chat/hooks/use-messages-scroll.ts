import type { IChatMessage } from 'src/types/chat';

import { useRef, useEffect, useCallback } from 'react';

// ----------------------------------------------------------------------

export type UseMessagesScrollReturn = {
  messagesEndRef: React.RefObject<HTMLDivElement>;
  scrollToBottom: () => void;
};

export function useMessagesScroll(messages: IChatMessage[]): UseMessagesScrollReturn {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const lastMessageCount = useRef(messages.length);

  const scrollToBottom = useCallback(() => {
    if (!messagesEndRef.current) {
      return;
    }

    messagesEndRef.current.scrollTop = messagesEndRef.current.scrollHeight;
  }, []);

  useEffect(() => {
    // Only scroll to bottom when a new message is added (not when content is appended)
    if (messages.length > lastMessageCount.current) {
      scrollToBottom();
    }
    lastMessageCount.current = messages.length;
  }, [messages.length, scrollToBottom]);

  return { messagesEndRef, scrollToBottom };
}
