import type { SxProps } from '@mui/material';
import type { IChatMessage, IChatParticipant } from 'src/types';

import { Box } from '@mui/material';

import { AUTH } from 'src/lib/firebase';
import { AiAgentIcon } from 'src/assets/icons';

import { ChatMessageList } from './chat-message-list';
import { ChatMessageInput } from './chat-message-input';

interface Props {
  containerSx?: SxProps;
  messageListSx?: SxProps;
  inputSx?: SxProps;
  messages: IChatMessage[];
  loading: boolean;
  isReplying: boolean;
  disabled: boolean;
  sendMessage: (message: string) => Promise<void>;
}

const ChatBox: React.FC<Props> = ({
  containerSx,
  disabled,
  isReplying,
  loading,
  messages,
  sendMessage,
  messageListSx,
  inputSx,
}) => {
  const chatParticipants: IChatParticipant[] = [
    {
      id: AUTH.currentUser?.uid ?? 'user',
      name: AUTH.currentUser?.displayName ?? 'User',
      avatarUrl: AUTH.currentUser?.photoURL ?? '',
    },
    {
      id: 'bot',
      name: '<PERSON><PERSON>',
      avatar: <AiAgentIcon sx={{ width: 32, height: 32 }} />,
    },
  ];

  return (
    <Box sx={{ ...containerSx }}>
      <ChatMessageList
        messages={messages}
        participants={chatParticipants}
        loading={loading}
        isReplying={isReplying}
        sx={{ ...messageListSx }}
      />
      <ChatMessageInput disabled={disabled} onSendMessage={sendMessage} sx={{ ...inputSx }} />
    </Box>
  );
};

export default ChatBox;
