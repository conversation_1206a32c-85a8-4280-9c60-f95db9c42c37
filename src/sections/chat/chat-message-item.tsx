import type { IChatMessage, IChatParticipant } from 'src/types';

import { toast } from 'sonner';
import { marked } from 'marked';
import { useCallback } from 'react';
import { useCopyToClipboard } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { Tooltip, useTheme, CircularProgress } from '@mui/material';

import { AUTH } from 'src/lib/firebase';

import { Iconify } from 'src/components/iconify';
import { Markdown } from 'src/components/markdown';

import { getMessage } from './utils/get-message';
import useConvertToResource from '../resources/hooks/convert-to-resource';

// ----------------------------------------------------------------------

type Props = {
  message: IChatMessage;
  participants: IChatParticipant[];
};

export function ChatMessageItem({ message, participants }: Props) {
  const user = AUTH.currentUser;
  const theme = useTheme();

  const { copy } = useCopyToClipboard();
  const { convertTextToResource, isConverting } = useConvertToResource();

  const { me, senderDetails } = getMessage({
    message,
    participants,
    currentUserId: user?.uid ?? 'user',
  });

  const { firstName, avatar, avatarUrl } = senderDetails;

  const { body } = message;

  const onCopy = useCallback(
    async (text: string) => {
      if (text) {
        // Convert markdown to HTML
        const htmlContent = await marked(text);

        // Try to copy as formatted HTML
        navigator.clipboard
          .write([
            new ClipboardItem({
              'text/html': new Blob([htmlContent], { type: 'text/html' }),
              'text/plain': new Blob([text], { type: 'text/plain' }),
            }),
          ])
          .catch(() => {
            // Fallback to plain text copy
            copy(text);
          })
          .finally(() => {
            toast.success('Copied!');
          });
      }
    },
    [copy]
  );

  const renderInfo = () => (
    <Typography
      noWrap
      variant="caption"
      sx={{ mb: 1, color: 'text.disabled', ...(!me && { mr: 'auto' }) }}
    >
      {!me && `${firstName}`}
    </Typography>
  );

  const renderBody = () => (
    <Stack
      sx={{
        p: 1.5,
        minWidth: 48,
        maxWidth: 600,
        borderRadius: 1,
        typography: 'body2',
        bgcolor: 'background.neutral',
        ...(me && { color: theme.palette.common.white, bgcolor: 'primary.lighter' }),
      }}
    >
      {typeof body === 'string' ? (
        <Markdown sx={{ '& p': { typography: 'body2', margin: 0 } }}>{body}</Markdown>
      ) : (
        body
      )}
    </Stack>
  );

  const renderActions = () => {
    if (typeof body !== 'string') {
      return null;
    }

    return (
      <Box
        className="message-actions"
        sx={{
          pt: 0.5,
          opacity: 0,
          top: '100%',
          display: 'flex',
          position: 'absolute',
          transition: theme.transitions.create(['opacity'], {
            duration: theme.transitions.duration.shorter,
          }),
          right: 0,
          left: 'unset',
          gap: 0.5,
        }}
      >
        <Tooltip title="Copy" arrow>
          <IconButton size="small" onClick={() => onCopy(body)}>
            <Iconify color={theme.palette.grey[700]} icon="solar:copy-bold" width={16} />
          </IconButton>
        </Tooltip>
        <Tooltip title="Convert to source" arrow>
          <IconButton
            disabled={isConverting}
            size="small"
            onClick={() => convertTextToResource(body)}
          >
            {isConverting ? (
              <CircularProgress size={16} />
            ) : (
              <Iconify
                color={theme.palette.grey[700]}
                icon="material-symbols:convert-to-text-rounded"
                width={16}
              />
            )}
          </IconButton>
        </Tooltip>
      </Box>
    );
  };

  if (!message.body) {
    return null;
  }

  return (
    <Box sx={{ mb: 5, display: 'flex', justifyContent: me ? 'flex-end' : 'unset' }}>
      {!me && (
        <>
          {avatar ? (
            <Box sx={{ mr: 2 }}>{avatar}</Box>
          ) : (
            <Avatar alt={firstName} src={avatarUrl} sx={{ width: 32, height: 32, mr: 2 }} />
          )}
        </>
      )}

      <Stack alignItems={me ? 'flex-end' : 'flex-start'}>
        {renderInfo()}

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            position: 'relative',
            '&:hover': { '& .message-actions': { opacity: 1 } },
          }}
        >
          {renderBody()}
          {renderActions()}
        </Box>
      </Stack>
    </Box>
  );
}
