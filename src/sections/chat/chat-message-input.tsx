import type { Theme, SxProps } from '@mui/material';

import { useState, useCallback } from 'react';

import InputBase from '@mui/material/InputBase';
import { Tooltip, useTheme } from '@mui/material';
import IconButton from '@mui/material/IconButton';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

type Props = {
  disabled?: boolean;
  onSendMessage: (message: string) => Promise<void>;
  sx?: SxProps<Theme>;
};

export function ChatMessageInput({ disabled, onSendMessage, sx = {} }: Props) {
  const theme = useTheme();
  const [message, setMessage] = useState('');

  const handleChangeMessage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(event.target.value);
  }, []);

  const handleKeyUp = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleSendMessage();
    }
  };

  const handleSendMessage = async () => {
    if (!message || disabled) return;
    onSendMessage(message);
    setMessage('');
  };

  return (
    <InputBase
      name="chat-message"
      id="chat-message-input"
      value={message}
      onKeyUp={handleKeyUp}
      onChange={handleChangeMessage}
      placeholder="Start typing..."
      endAdornment={
        <Tooltip title="Send" placement="top" arrow>
          <IconButton disabled={disabled} onClick={handleSendMessage}>
            <Iconify icon="mynaui:send-solid" />
          </IconButton>
        </Tooltip>
      }
      sx={{
        px: 1,
        height: 64,
        flexShrink: 0,
        borderTop: `solid 1px ${theme.vars.palette.divider}`,
        ...sx,
      }}
    />
  );
}
