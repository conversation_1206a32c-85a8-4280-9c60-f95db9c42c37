import type { Theme, SxProps } from '@mui/material';
import type { IChatMessage, IChatParticipant } from 'src/types';

import { useTheme } from '@mui/material';

import { fSub } from 'src/utils/format-time';

import { Scrollbar } from 'src/components/scrollbar';
import AnimateDots from 'src/components/animate/animate-dots';
import { LoadingScreen } from 'src/components/loading-screen';

import { ChatMessageItem } from './chat-message-item';
import { useMessagesScroll } from './hooks/use-messages-scroll';

// ----------------------------------------------------------------------

type Props = {
  loading: boolean;
  messages: IChatMessage[];
  participants: IChatParticipant[];
  isReplying: boolean;
  sx?: SxProps<Theme>;
};

export function ChatMessageList({
  messages = [],
  participants,
  loading,
  isReplying,
  sx = {},
}: Props) {
  const theme = useTheme();
  const { messagesEndRef, scrollToBottom } = useMessagesScroll(messages);

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Scrollbar
      ref={messagesEndRef}
      sx={{
        px: 3,
        pt: 5,
        pb: 3,
        flex: '1 1 auto',
        height: 300,
        [theme.breakpoints.down('sm')]: {
          px: 0,
          pt: 2,
        },
        '& .simplebar-content': {
          transition: 'all 0.3s ease-in-out',
        },
        ...sx,
      }}
    >
      {messages.map((message) => (
        <ChatMessageItem key={message.id} message={message} participants={participants} />
      ))}
      {isReplying && (
        <ChatMessageItem
          participants={participants}
          message={{
            id: '1',
            body: <AnimateDots size={6} sx={{ mb: 1 }} />,
            senderId: 'bot',
            createdAt: fSub({ minutes: 1 }),
          }}
        />
      )}
    </Scrollbar>
  );
}
