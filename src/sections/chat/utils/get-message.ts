import type { IChatMessage, IChatParticipant } from 'src/types';

// ----------------------------------------------------------------------

type Props = {
  currentUserId: string;
  message: IChatMessage;
  participants: IChatParticipant[];
};

export function getMessage({ message, participants, currentUserId }: Props) {
  const sender = participants.find((participant) => participant.id === message.senderId);

  const isCurrentUser = message.senderId === currentUserId;

  const senderDetails = isCurrentUser
    ? { type: 'me' }
    : {
        avatar: sender?.avatar,
        avatarUrl: sender?.avatarUrl,
        firstName: sender?.name?.split(' ')[0] ?? 'Unknown',
      };

  return { me: isCurrentUser, senderDetails };
}
