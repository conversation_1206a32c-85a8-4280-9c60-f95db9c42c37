import type { Resource } from 'src/types';
import type { TransitionProps } from '@mui/material/transitions';

import { useMemo, useEffect, forwardRef } from 'react';

import Slide from '@mui/material/Slide';
import AppBar from '@mui/material/AppBar';
import Dialog from '@mui/material/Dialog';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { Stack, Divider, useTheme, useMediaQuery } from '@mui/material';

import useAiAgent from 'src/hooks/ai-agent';

import { DIALOG_APP_BAR_HEIGHT } from 'src/theme/constants';
import { useGetProjectDetailsQuery } from 'src/store/api/projects';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

import ChatBox from './chat-box';
import ProjectCard from '../projects/components/project-card';
import ResourceCard from '../resources/components/resource-card';

// ----------------------------------------------------------------------

const Transition = forwardRef(
  (props: TransitionProps & { children: React.ReactElement }, ref: React.Ref<unknown>) => (
    <Slide direction="up" ref={ref} {...props} />
  )
);

interface Props {
  open: boolean;
  onClose: () => void;
  resources?: Resource[];
  initialMessages?: string[];
  projectId?: string;
}

const ChatDialog: React.FC<Props> = ({
  open,
  onClose,
  resources,
  projectId,
  initialMessages = [],
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: project, isLoading: isLoadingProjectDetails } = useGetProjectDetailsQuery(
    { id: projectId as string, filter: { includeAllResources: true } },
    {
      skip: !projectId,
      selectFromResult(state) {
        return {
          data: state.data,
          isLoading: state.isLoading,
        };
      },
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
    }
  );

  const {
    messages,
    isReplying,
    allowSendMessage,
    sendMessage,
    startConversationWithResources,
    startConversationForProject,
    hasActiveSession,
  } = useAiAgent();

  const loading = isLoadingProjectDetails;

  const title = useMemo(() => {
    if (project) {
      return `Aida Chat for project: ${project.name}`;
    } else if (resources) {
      return `Aida Chat for ${resources.length} selected resources`;
    }

    return 'Aida Chat';
  }, [project, resources]);

  const inputPreviews = useMemo(() => {
    if (project) {
      return <ProjectCard data={project} hideActions />;
    } else if (resources) {
      return resources.map((resource) => <ResourceCard data={resource} hideActions hidePreview />);
    }

    return null;
  }, [project, resources]);

  useEffect(() => {
    if (!open) return;

    if (project) {
      startConversationForProject(project);
    } else if (resources) {
      startConversationWithResources(resources);
    }
  }, [open, resources, project]);

  useEffect(() => {
    if (hasActiveSession && initialMessages.length) {
      initialMessages.forEach((message) => sendMessage(message));
    }
  }, [hasActiveSession, initialMessages]);

  return (
    <Dialog fullScreen open={open} onClose={onClose} TransitionComponent={Transition}>
      <AppBar position="relative" color="default">
        <Toolbar>
          <Typography variant="h6" sx={{ flex: 1 }}>
            {title}
          </Typography>

          <IconButton color="inherit" edge="start" onClick={onClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Toolbar>
      </AppBar>
      <Stack direction="row" height={`calc(100vh - ${DIALOG_APP_BAR_HEIGHT}px)`} gap={1}>
        {!isMobile && inputPreviews && (
          <>
            <Stack direction="column" width="25%" sx={{ p: 2 }}>
              <Scrollbar
                sx={[{ height: '100%' }]}
                slotProps={{ contentSx: { display: 'flex', flexDirection: 'column', gap: 2 } }}
              >
                {inputPreviews}
              </Scrollbar>
            </Stack>
            <Divider orientation="vertical" flexItem />
          </>
        )}

        <Stack direction="column" width={isMobile ? '100%' : '75%'}>
          <ChatBox
            containerSx={{
              px: isMobile ? 1 : 3,
              py: 1,
              height: '100%',
              position: 'relative',
              overflow: 'hidden',
            }}
            messageListSx={{ height: '90%' }}
            inputSx={{ width: '100%' }}
            messages={messages}
            loading={loading}
            isReplying={isReplying}
            disabled={!allowSendMessage}
            sendMessage={sendMessage}
          />
        </Stack>
      </Stack>
    </Dialog>
  );
};

export default ChatDialog;
