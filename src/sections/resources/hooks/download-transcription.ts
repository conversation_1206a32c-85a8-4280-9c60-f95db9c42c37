import type { Transcription } from 'src/types';

import FileSaver from 'file-saver';
import { Packer, TextRun, Document, Paragraph } from 'docx';

import { fSecsToTime } from 'src/utils/format-time';

const useDownloadTranscription = () => {
  const generateDocument = (transcriptions: Transcription[]) => {
    const document = new Document({
      sections: [
        {
          children: transcriptions.map(
            (item) =>
              new Paragraph({
                children: [
                  new TextRun({
                    text: `${item.nameFromRevAi} (${fSecsToTime(item.startTime)} - ${fSecsToTime(item.endTime)}): \n`,
                    bold: true,
                    break: 1,
                  }),
                  new TextRun({ text: item.content }),
                ],
              })
          ),
        },
      ],
    });

    return document;
  };

  const download = (transcriptions: Transcription[], fileName = 'transcription.docx') => {
    if (!transcriptions.length) return;

    const doc = generateDocument(transcriptions);
    Packer.toBlob(doc).then((blob) => {
      FileSaver.saveAs(blob, fileName);
    });
  };

  return download;
};

export default useDownloadTranscription;
