import { useMemo } from 'react';

import { useGetResourcesQuery } from 'src/store/api/resources';

import { convertResourceToResourceItem } from '../utils';

import type { ResourceItem } from '../components/resources-list';

const useGetResources = () => {
  const { data: resourcesData, isLoading } = useGetResourcesQuery(
    {},
    {
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
    }
  );

  const resources = useMemo<ResourceItem[]>(
    () => (resourcesData ?? []).map(convertResourceToResourceItem),
    [resourcesData]
  );

  return {
    resources,
    isLoading,
  };
};

export default useGetResources;
