import { useState, useEffect } from 'react';

import { TranscriptStatus } from 'src/types';
import { useGetResourceQuery } from 'src/store/api/resources';

const POLLING_INTERVAL = 10000;

const useCheckResourceStatus = (resourceId?: string) => {
  const [pollingInterval, setPollingInterval] = useState<number>(POLLING_INTERVAL);

  const { data } = useGetResourceQuery(
    {
      id: resourceId ?? '',
    },
    {
      skip: !resourceId,
      pollingInterval,
    }
  );

  // Stop polling when the resource is completed
  useEffect(() => {
    if (data?.transcriptionJobStatus === TranscriptStatus.Completed) {
      setPollingInterval(0);
    }
  }, [data, pollingInterval]);
};

export default useCheckResourceStatus;
