import type { Resource } from 'src/types';

import { Stack, Typography } from '@mui/material';

import { TranscriptStatus } from 'src/types';

import TranscodingStatus from 'src/components/transcoding-status';

import type { ResourceItem } from './components/resources-list';

export const getTranscriptionStatusDisplay = (transcriptionStatus?: TranscriptStatus) => {
  switch (transcriptionStatus) {
    case TranscriptStatus.Completed:
      return 'Transcription ready';
    case TranscriptStatus.InProgress:
      return 'Transcription in progress...';
    case TranscriptStatus.Failed:
      return 'Transcription failed';
    case TranscriptStatus.UnTranscript:
      return 'Transcription not started';
    default:
      return '';
  }
};

export const convertResourceToResourceItem = (resource: Resource): ResourceItem => ({
  ...resource,
  cardType: 'resource',
  title:
    resource.transcriptionJobStatus === TranscriptStatus.Completed ? null : (
      <Stack direction="row" gap={1} alignItems="center">
        <Typography
          variant="body2"
          color="textSecondary"
          component="div"
          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
        >
          Status:
          <Typography variant="body2" color="info" fontWeight={600}>
            {getTranscriptionStatusDisplay(resource.transcriptionJobStatus)}
          </Typography>
        </Typography>
        {resource.isTranscoding && <TranscodingStatus />}
      </Stack>
    ),
});
