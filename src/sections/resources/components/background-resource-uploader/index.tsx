import type { ResourceUploadQueueItem } from 'src/types';

import axios from 'axios';
import { toast } from 'sonner';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Link, Button } from '@mui/material';

import useAnalytics from 'src/hooks/analytics';
import useUserInitialContext from 'src/hooks/user-initial-context';

import axiosInstance from 'src/lib/axios';
import { useCreateResourceMutation } from 'src/store/api/resources';
import { selectResourceUploadQueue } from 'src/store/slices/resources/selectors';
import { updateUploadQueueItem, clearCompletedUploads } from 'src/store/slices/resources/slice';

import { fileFormat, fileTypeByUrl } from 'src/components/file-thumbnail/utils';

const BackgroundResourceUploader = () => {
  const { trackEvent } = useAnalytics();
  const dispatch = useDispatch();
  const { getFolderLocation } = useUserInitialContext();
  const [triggerCreateResource] = useCreateResourceMutation();

  const uploadQueue = useSelector(selectResourceUploadQueue);

  const updateQueueItem = (id: string, data: Partial<ResourceUploadQueueItem>) => {
    dispatch(updateUploadQueueItem({ id, data }));
  };

  const processQueueItem = async (item: ResourceUploadQueueItem) => {
    const { file, uploadUrlData, projectId, folderId } = item;
    const { fields, url } = uploadUrlData;
    const uploadedFileName = fields['x-ignore-file-name'];
    const formData = new FormData();

    Object.entries({ ...fields, file }).forEach(([key, value]) => {
      formData.append(key, value as any);
    });

    const fileType = fileTypeByUrl(file.name);
    const fFormat = fileFormat(file.name);
    const isAudioOrVideo = fFormat === 'audio' || fFormat === 'video';

    try {
      const source = axios.CancelToken.source();
      trackEvent({
        eventCategory: 'Resource',
        eventAction: 'Upload file',
        properties: {
          fileName: file.name,
          fileSize: file.size,
        },
      });
      updateQueueItem(item.id, { status: 'uploading', cancelToken: source });
      await axiosInstance.post(url, formData, {
        cancelToken: source.token,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (event) => {
          if (event.lengthComputable) {
            // Update progress every 1s
            setTimeout(() => {
              const progress = Math.round((event?.progress ?? 0) * 100);
              updateQueueItem(item.id, {
                progress,
              });
            }, 1000);
          }
        },
      });

      // Trigger resource creation
      updateQueueItem(item.id, { status: 'processing' });

      const payload = {
        fileName: file.name,
        fileSize: file.size,
        fileLastModified: new Date(file.lastModified).toISOString(),
        uploadedFileName,
        projectId,
        folderId,
        transcoded: item.transcoded,
        mode: item.mode,
      };

      await triggerCreateResource({
        payload,
      }).unwrap();

      trackEvent({
        eventCategory: 'Resource',
        eventAction: 'File uploaded successfully',
        properties: {
          ...payload,
          fileType,
          isAudioOrVideo,
        },
      });

      // Update item as completed
      updateQueueItem(item.id, { status: 'completed' });

      const location = getFolderLocation(projectId, folderId);

      toast.success('File uploaded', {
        description: `${file.name} has been uploaded successfully`,
        duration: 10000,
        action: location ? (
          <Link href={location.url} target="_self" sx={{ color: 'text.primary', flexShrink: 0 }}>
            <Button variant="text" size="small">
              Go to folder
            </Button>
          </Link>
        ) : null,
      });

      // Auto-cleanup completed uploads after 3 seconds to prevent duplicate display
      setTimeout(() => {
        dispatch(clearCompletedUploads());
      }, 3000);
    } catch (error: any) {
      if (axios.isCancel(error)) {
        updateQueueItem(item.id, { status: 'cancelled' });
      } else {
        console.error('Error uploading file:', error);
        toast.error('Failed to upload', {
          description: `Something wrong when uploading file: ${file.name}`,
        });
      }
    }
  };

  const processQueue = () => {
    uploadQueue.forEach((item, idx) => {
      const prevItem = uploadQueue[idx - 1];
      const isPrevItemCompleted = prevItem
        ? prevItem.status && ['completed', 'failed'].includes(prevItem.status)
        : true;

      if (isPrevItemCompleted && item.status === 'pending') {
        processQueueItem(item);
      }
    });
  };

  useEffect(() => {
    if (!uploadQueue.length) return;
    processQueue();
  }, [uploadQueue]);

  // Cleanup completed uploads periodically to prevent accumulation
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const hasCompletedUploads = uploadQueue.some((item) => item.status === 'completed');
      if (hasCompletedUploads) {
        dispatch(clearCompletedUploads());
      }
    }, 5000); // Check every 5 seconds

    return () => clearInterval(cleanupInterval);
  }, [uploadQueue, dispatch]);

  return null;
};

export default BackgroundResourceUploader;
