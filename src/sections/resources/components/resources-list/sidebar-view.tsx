import type { Resource } from 'src/types';

import { memo } from 'react';

import { Box, Card, useTheme, useMediaQuery } from '@mui/material';

import { GLOBAL_HEADER_HEIGHT } from 'src/theme/constants';

import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content';

import ResourceCard from '../resource-card';
import { RESOURCE_LIST_HEADER_HEIGHT } from '../../types';

import type { ResourceItem } from '.';

const ResourcesSidebarView: React.FC<{ focusedResource: Resource; items: ResourceItem[] }> = ({
  focusedResource,
  items,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const filteredItems = items.filter((item) => item.id !== focusedResource.id);

  const hasOtherItems = filteredItems.length > 0;

  const offset = GLOBAL_HEADER_HEIGHT + RESOURCE_LIST_HEADER_HEIGHT + 50;

  return (
    <Box
      sx={{
        gap: 2,
        display: 'flex',
        width: '100%',
        maxHeight: ['100%', `calc(100vh - ${offset}px)`],
        flexDirection: isMobile ? 'column-reverse' : 'row',
      }}
    >
      {hasOtherItems ? (
        <Scrollbar
          sx={[{ height: '100%', width: !isMobile ? '25%' : '100%' }]}
          slotProps={{ contentSx: { display: 'flex', flexDirection: 'column' } }}
        >
          {filteredItems.map((item) => (
            <ResourceCard
              hidePreview
              data={item}
              key={item.id}
              sx={{ height: 'fit-content', mb: 1 }}
            />
          ))}
        </Scrollbar>
      ) : (
        <Card sx={{ height: 250, width: !isMobile ? '25%' : '100%', borderRadius: 1 }}>
          <EmptyContent title="No other files" />
        </Card>
      )}
      <Box sx={{ width: hasOtherItems && !isMobile ? '75%' : '100%' }}>
        <ResourceCard key={focusedResource.id} data={focusedResource} view="details" />
      </Box>
    </Box>
  );
};

export default memo(ResourcesSidebarView);
