import { memo } from 'react';

import { Grid2 as Grid } from '@mui/material';

import ResourceCard from '../resource-card';

import type { ResourceItem } from '.';

const ResourcesGridView: React.FC<{ items: ResourceItem[] }> = ({ items }) => (
  <Grid
    container
    spacing={2}
    columns={{ xs: 12, sm: 8, md: 8, lg: 12, xl: 16 }}
    sx={{ width: '100%' }}
  >
    {items.map((item) => (
      <Grid key={item.id} size={{ xs: 12, sm: 4, md: 4, lg: 4, xl: 4 }}>
        <ResourceCard data={item} />
      </Grid>
    ))}
  </Grid>
);

export default memo(ResourcesGridView);
