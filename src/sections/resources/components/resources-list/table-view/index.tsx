import type { TableHeadCellProps } from 'src/components/table';

import { memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  Box,
  Stack,
  Table,
  TableBody,
  TableContainer,
  tableCellClasses,
  tablePaginationClasses,
} from '@mui/material';

import useAnalytics from 'src/hooks/analytics';

import { setTableDense } from 'src/store/slices/settings/slice';
import { selectTableDense } from 'src/store/slices/settings/selectors';
import { selectSelectedResourceIds } from 'src/store/slices/resources/selectors';
import {
  viewResource,
  selectAllResources,
  toggleSelectResource,
  clearSelectedResources,
} from 'src/store/slices/resources/slice';

import {
  useTable,
  TableNoData,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import FileTableRow from './file-table-row';

import type { ResourceItem } from '..';

const TABLE_HEAD: TableHeadCellProps[] = [
  { id: 'name', label: 'Name' },
  { id: 'location', label: 'Location', width: 120 },
  { id: 'size', label: 'Size', width: 120 },
  { id: 'type', label: 'Type', width: 120 },
  { id: 'modifiedAt', label: 'Modified', width: 140 },
  { id: '', width: 88 },
];

const ResourcesTableView: React.FC<{ items: ResourceItem[] }> = ({ items }) => {
  const { trackEvent } = useAnalytics();
  const dispatch = useDispatch();
  const selectedResourceIds = useSelector(selectSelectedResourceIds);
  const defaultDense = useSelector(selectTableDense);

  const table = useTable({ defaultRowsPerPage: 10, defaultDense });
  const {
    dense,
    page,
    order,
    orderBy,
    rowsPerPage,
    onSort,
    onChangePage,
    onChangeRowsPerPage,
    onChangeDense,
  } = table;

  const handleSelectRow = (id: string) => {
    const file = items.find((item) => item.id === id);
    if (file) {
      dispatch(toggleSelectResource(file));
    }
  };

  const handleChangeDense = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    dispatch(setTableDense(checked));
    onChangeDense?.(event);
  };

  const handleSelectAllRows = (checked: boolean) => {
    if (checked) {
      dispatch(selectAllResources(items));
    } else {
      dispatch(clearSelectedResources());
    }
  };

  const handleViewResource = (id: string) => {
    const data = items.find((item) => item.id === id);
    if (!data) return;

    dispatch(viewResource(data));
    trackEvent({
      eventCategory: 'Resource',
      eventAction: 'View resource',
      properties: {
        resourceId: data.id,
        resourceName: data.name,
      },
    });
  };

  return (
    <Stack direction="column" gap={1}>
      <Box sx={[(theme) => ({ position: 'relative', m: { md: theme.spacing(-2, -3, 0, -3) } })]}>
        <TableContainer sx={{ px: { md: 3 } }}>
          <Table
            size={dense ? 'small' : 'medium'}
            sx={{ minWidth: 960, borderCollapse: 'separate', borderSpacing: '0 16px' }}
          >
            <TableHeadCustom
              order={order}
              orderBy={orderBy}
              headCells={TABLE_HEAD}
              rowCount={items.length}
              numSelected={selectedResourceIds.length}
              onSort={onSort}
              onSelectAllRows={handleSelectAllRows}
              sx={{
                [`& .${tableCellClasses.head}`]: {
                  '&:first-of-type': { borderTopLeftRadius: 12, borderBottomLeftRadius: 12 },
                  '&:last-of-type': { borderTopRightRadius: 12, borderBottomRightRadius: 12 },
                },
              }}
            />

            <TableBody>
              {items.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row) => (
                <FileTableRow
                  key={row.id}
                  row={row}
                  selected={selectedResourceIds.includes(row.id)}
                  onSelectRow={() => handleSelectRow(row.id)}
                  onView={() => handleViewResource(row.id)}
                />
              ))}

              <TableNoData
                notFound={false}
                sx={[
                  (theme) => ({
                    m: -2,
                    borderRadius: 1.5,
                    border: `dashed 1px ${theme.vars.palette.divider}`,
                  }),
                ]}
              />
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      <TablePaginationCustom
        page={page}
        dense={dense}
        rowsPerPage={rowsPerPage}
        count={items.length}
        onPageChange={onChangePage}
        onChangeDense={handleChangeDense}
        onRowsPerPageChange={onChangeRowsPerPage}
        sx={{ [`& .${tablePaginationClasses.toolbar}`]: { borderTopColor: 'transparent' } }}
      />
    </Stack>
  );
};

export default memo(ResourcesTableView);
