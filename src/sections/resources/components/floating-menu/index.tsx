import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useBoolean } from 'minimal-shared/hooks';

import { Paper, Stack, styled, Button, Tooltip, IconButton, Typography } from '@mui/material';

import { useAppSelector } from 'src/store';
import { AiAgentIcon } from 'src/assets/icons';
import { clearSelectedResources } from 'src/store/slices/resources/slice';
import { selectSelectedResources } from 'src/store/slices/resources/selectors';

import { Iconify } from 'src/components/iconify';

import ChatDialog from 'src/sections/chat/chat-dialog';

const Wrapper = styled(Paper)(({ theme }) =>
  theme.unstable_sx({
    position: 'fixed',
    zIndex: 1200,
    bottom: 24,
    left: 0,
    right: 0,
    marginInline: 'auto',
    width: '50%',
    p: 2,
    backgroundColor: theme.palette.background.neutral,
    [theme.breakpoints.down('sm')]: {
      width: '90%',
    },
  })
);

const SUMMARISE_MESSAGE = 'Summarise';

const ResourcesFloatingMenu: React.FC = () => {
  const dispatch = useDispatch();
  const openChatDialog = useBoolean();
  const selectedResources = useAppSelector(selectSelectedResources);

  // Initial messages for conversation with Aida
  const [initialMessages, setInitialMessages] = useState<string[]>([]);

  const handleSummarise = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setInitialMessages([SUMMARISE_MESSAGE]);
    openChatDialog.onTrue();
  };

  const handleOpenChat = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setInitialMessages([]);
    openChatDialog.onTrue();
  };

  if (!selectedResources.length) return null;

  const onClearAll = () => {
    dispatch(clearSelectedResources());
  };

  return (
    <>
      <Wrapper elevation={3}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" gap={1} alignItems="center">
            <Tooltip title="Aida Chat" arrow placement="top">
              <IconButton color="primary" onClick={handleOpenChat}>
                <AiAgentIcon sx={{ width: 24, height: 24 }} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Summarise the selected files" arrow placement="top">
              <Button variant="outlined" color="secondary" size="small" onClick={handleSummarise}>
                {SUMMARISE_MESSAGE}
              </Button>
            </Tooltip>
          </Stack>
          <Stack direction="row" gap={1} alignItems="center">
            <Typography variant="subtitle2">
              {selectedResources.length} files(s) selected
            </Typography>
            <Tooltip title="Clear selection" arrow placement="top">
              <IconButton color="default" onClick={onClearAll}>
                <Iconify sx={{ width: 24, height: 24 }} icon="solar:close-circle-linear" />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
      </Wrapper>

      <ChatDialog
        open={openChatDialog.value}
        onClose={openChatDialog.onFalse}
        resources={selectedResources}
        key={`chat-dialog-${selectedResources.map((r) => r.id).join('-')}`}
        initialMessages={initialMessages}
      />
    </>
  );
};

export default ResourcesFloatingMenu;
