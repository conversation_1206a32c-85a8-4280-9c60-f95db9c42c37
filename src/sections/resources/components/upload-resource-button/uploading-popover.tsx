import type { ResourceUploadQueueItem } from 'src/types';
import type { CustomPopoverProps } from 'src/components/custom-popover';

import { useMemo, useState } from 'react';
import { varAlpha } from 'minimal-shared/utils';
import { useBoolean } from 'minimal-shared/hooks';
import { useDispatch, useSelector } from 'react-redux';

import FolderRoundedIcon from '@mui/icons-material/FolderRounded';
import {
  Box,
  Stack,
  Tooltip,
  MenuItem,
  Typography,
  IconButton,
  ListItemText,
  CircularProgress,
} from '@mui/material';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { fData } from 'src/utils/format-number';

import { removeFromUploadQueue } from 'src/store/slices/resources/slice';
import { selectSortedResourceUploadQueue } from 'src/store/slices/resources/selectors';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { CustomPopover } from 'src/components/custom-popover';
import { fileThumb, fileFormat } from 'src/components/file-thumbnail/utils';

import UploadResourceDialog from '../upload-resource-dialog';

const UploadButton = () => {
  const uploadDialog = useBoolean();
  return (
    <>
      <Box
        onClick={uploadDialog.onTrue}
        sx={[
          (theme) => ({
            width: '100%',
            p: 1,
            flexShrink: 0,
            display: 'flex',
            borderRadius: 1,
            cursor: 'pointer',
            alignItems: 'center',
            color: 'text.disabled',
            justifyContent: 'center',
            bgcolor: varAlpha(theme.vars.palette.grey['500Channel'], 0.08),
            border: `dashed 1px ${varAlpha(theme.vars.palette.grey['500Channel'], 0.16)}`,

            '&:hover': { opacity: 0.72 },
          }),
        ]}
      >
        <Box
          sx={{
            gap: 1,
            display: 'flex',
            alignItems: 'center',
            color: 'text.disabled',
          }}
        >
          <Iconify icon="eva:cloud-upload-fill" width={24} />
          <Typography variant="body2">Upload file</Typography>
        </Box>
      </Box>

      {uploadDialog.value && (
        <UploadResourceDialog open={uploadDialog.value} onClose={uploadDialog.onFalse} />
      )}
    </>
  );
};

const UploadItem: React.FC<{ data: ResourceUploadQueueItem }> = ({ data }) => {
  const dispatch = useDispatch();
  const { getFolderLocation } = useUserInitialContext();
  const { id, file, progress, status, cancelToken, projectId, folderId } = data;
  const [isHovered, setIsHovered] = useState(false);

  const format = fileFormat(file.name);

  const statusText = useMemo(() => {
    switch (status) {
      case 'pending':
        return 'Upload pending';
      case 'uploading':
        return 'Uploading...';
      case 'completed':
        return 'Completed';
      case 'processing':
        return 'Processing...';
      case 'failed':
        return 'Upload failed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return '';
    }
  }, [status]);

  const showCancelAction = isHovered && status !== 'processing';
  const shouldRemove = status && ['pending', 'completed', 'failed', 'cancelled'].includes(status);

  const location = getFolderLocation(projectId, folderId);

  const handleCancel = () => {
    if (shouldRemove) {
      dispatch(removeFromUploadQueue({ id }));
      return;
    }

    cancelToken?.cancel();
  };

  return (
    <MenuItem onMouseOver={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <Box component="img" src={fileThumb(format)} />

      <ListItemText
        primary={file.name}
        secondary={
          <Stack>
            <Stack direction="row" alignItems="center">
              {fData(file.size)}
              <Box
                sx={{
                  mx: 0.75,
                  width: 2,
                  height: 2,
                  borderRadius: '50%',
                  bgcolor: 'currentColor',
                }}
              />
              {statusText}
            </Stack>
            {location && (
              <Stack direction="row" alignItems="center" gap={0.5}>
                <FolderRoundedIcon fontSize="small" color="action" sx={{ width: 12, height: 12 }} />
                {location.name}
              </Stack>
            )}
          </Stack>
        }
        primaryTypographyProps={{ noWrap: true, typography: 'subtitle2' }}
        secondaryTypographyProps={{
          mt: 0.5,
          component: 'span',
          alignItems: 'center',
          typography: 'caption',
          color: 'text.disabled',
          display: 'inline-flex',
        }}
      />
      {showCancelAction ? (
        <Tooltip title={shouldRemove ? 'Remove' : 'Cancel upload'}>
          <IconButton onClick={handleCancel}>
            <Iconify icon="solar:close-circle-bold" />
          </IconButton>
        </Tooltip>
      ) : (
        <Box sx={{ position: 'relative', display: 'inline-flex' }}>
          <CircularProgress
            color={progress === 100 ? 'success' : 'primary'}
            variant="determinate"
            value={progress}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: 'absolute',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography
              variant="caption"
              component="div"
              sx={{ color: 'text.secondary', fontSize: 10, fontWeight: 600 }}
            >{`${progress ?? 0}%`}</Typography>
          </Box>
        </Box>
      )}
    </MenuItem>
  );
};

const UploadingPopover: React.FC<CustomPopoverProps> = (props) => {
  const uploads = useSelector(selectSortedResourceUploadQueue);

  // Reverse to show the processing uploads first
  const reversedUploads = useMemo(() => [...uploads].reverse(), [uploads]);

  return (
    <CustomPopover {...props} slotProps={{ arrow: { offset: 20 }, paper: { sx: { p: 2 } } }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Recent uploads <span>({uploads.length})</span>
      </Typography>
      <UploadButton />

      <Scrollbar
        sx={{ height: 'auto', maxHeight: 400, width: 'fit-content', minWidth: 350, mt: 1 }}
      >
        {reversedUploads.map((item) => (
          <UploadItem data={item} key={item.id} />
        ))}
      </Scrollbar>
    </CustomPopover>
  );
};

export default UploadingPopover;
