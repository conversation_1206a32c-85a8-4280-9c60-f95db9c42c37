import type { Resource } from 'src/types';

import { toast } from 'sonner';
import { useDispatch, useSelector } from 'react-redux';

import LoadingButton from '@mui/lab/LoadingButton';
import { Button, Dialog, DialogTitle, DialogActions, DialogContent } from '@mui/material';

import { viewResource } from 'src/store/slices/resources/slice';
import { useDeleteSessionMutation } from 'src/store/api/sessions';
import { useDeleteResourceMutation } from 'src/store/api/resources';
import { selectFocusedResource } from 'src/store/slices/resources/selectors';

import { Iconify } from 'src/components/iconify';

interface Props {
  resource: Resource;
  open: boolean;
  onClose: () => void;
  isSessionCard?: boolean;
}
const RemoveResourceDialog: React.FC<Props> = ({ onClose, open, resource, isSessionCard }) => {
  const dispatch = useDispatch();
  const focusedResource = useSelector(selectFocusedResource);

  const [triggerDeleteResource, { isLoading: isDeletingResource }] = useDeleteResourceMutation();
  const [triggerDeleteSession, { isLoading: isDeletingSession }] = useDeleteSessionMutation();

  const onDeleteResource = async (id: string) => {
    try {
      // If the deleted resource was selected, clear the selection
      if (focusedResource?.id === resource.id) {
        dispatch(viewResource(null));
      }

      onClose();
      toast.info('Deleting file...');

      await triggerDeleteResource({ id }).unwrap();
      toast.success('File deleted!');
    } catch (error) {
      toast.error('Failed to delete file');
    }
  };

  const onDeleteSession = async (id: string) => {
    try {
      onClose();
      toast.info('Deleting session...');

      await triggerDeleteSession({ id }).unwrap();
      toast.success('Session deleted!');
    } catch (error) {
      toast.error('Failed to delete session');
    }
  };

  const handleDelete = () => {
    if (!resource.id) return;

    if (isSessionCard) {
      onDeleteSession(resource.id);
    } else {
      onDeleteResource(resource.id);
    }
  };

  const isDeleting = isDeletingResource || isDeletingSession;

  return (
    <Dialog open={open} onClose={() => onClose()}>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Iconify icon="solar:trash-bin-minimalistic-bold" sx={{ color: 'error.main' }} />
        {isSessionCard ? 'Delete Session' : 'Delete File'}
      </DialogTitle>

      <DialogContent sx={{ color: 'text.secondary' }}>
        {isSessionCard
          ? 'Are you sure want to delete this session? This will stop the recording and remove bot from the call'
          : 'Are you sure want to delete this file? This action cannot be undone'}
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" onClick={onClose}>
          Cancel
        </Button>
        <LoadingButton
          variant="contained"
          color="error"
          onClick={handleDelete}
          autoFocus
          loading={isDeleting}
        >
          Confirm
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};

export default RemoveResourceDialog;
