import type { DialogProps } from '@mui/material';
import type { Resource } from 'src/types/resource';
import type { UserInitialContext } from 'src/types';

import { useDispatch } from 'react-redux';
import { useMemo, Fragment, useState } from 'react';

import LoadingButton from '@mui/lab/LoadingButton';
import {
  Box,
  Chip,
  Button,
  Dialog,
  Typography,
  DialogTitle,
  DialogActions,
  DialogContent,
} from '@mui/material';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { CONFIG } from 'src/global-config';
import { projectsService } from 'src/store/api/projects';
import { resourcesService, useUpdateResourceMutation } from 'src/store/api/resources';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { SvgColor } from 'src/components/svg-color';

import ProjectDropdown from 'src/sections/projects/components/project-dropdown';

interface Props extends Omit<DialogProps, 'resource'> {
  resource: Resource;
  open: boolean;
  onClose: () => void;
}
const MoveResourceDialog: React.FC<Props> = ({ resource, open, onClose, ...props }) => {
  const dispatch = useDispatch();
  const { projects } = useUserInitialContext();
  const [updateResource, { isLoading: isUpdating }] = useUpdateResourceMutation();

  const [selectedProjectId, setSelectedProjectId] = useState<string | undefined>(
    resource.projectId
  );

  const currentLocationDisplay = useMemo(() => {
    const locations: Array<{ name: string; type: 'project' | 'folder' }> = [];
    let currentProject: UserInitialContext['projects'][number] | null = null;
    if (resource.projectId) {
      currentProject = projects.find((project) => project.id === resource.projectId) ?? null;
      locations.push({ name: currentProject?.name ?? 'Unknown Project', type: 'project' });
    }
    if (resource.folderId && currentProject) {
      const currentFolder = currentProject.folders.find(
        (folder) => folder.id === resource.folderId
      );
      locations.push({ name: currentFolder?.name ?? 'Unknown Folder', type: 'folder' });
    }

    if (!locations.length) return null;
    return locations.map((location, idx) => (
      <Fragment key={location.name}>
        <Chip
          sx={{ width: 'fit-content', px: 0.5, gap: 0.5 }}
          icon={
            location.type === 'project' ? (
              <SvgColor
                color="primary"
                src={`${CONFIG.assetsDir}/assets/icons/navbar/ic-folder.svg`}
                sx={{ width: 14, height: 14, color: 'primary.main' }}
              />
            ) : (
              <Box
                component="img"
                src={`${CONFIG.assetsDir}/assets/icons/files/ic-folder.svg`}
                sx={{ width: 14, height: 14 }}
              />
            )
          }
          label={location.name}
          variant="outlined"
          size="small"
        />
        {idx < locations.length - 1 && <Iconify icon="eva:chevron-right-fill" />}
      </Fragment>
    ));
  }, [projects, resource.projectId, resource.folderId]);

  const handleMove = async () => {
    try {
      await updateResource({
        id: resource.id,
        payload: {
          projectId: selectedProjectId,
        },
      }).unwrap();
      // Invalidate source project and folder
      if (resource.projectId) {
        dispatch(
          projectsService.util.invalidateTags([
            {
              type: 'Projects',
              id: resource.projectId,
            },
            ...(resource.folderId
              ? [
                  {
                    type: 'ProjectFolders' as const,
                    id: resource.folderId,
                  },
                ]
              : []),
          ])
        );
      } else {
        dispatch(resourcesService.util.invalidateTags(['Resources']));
      }

      toast.success('File moved successfully');
      onClose();
    } catch (error) {
      toast.error('Failed to move file');
    }
  };

  const isSameLocation = selectedProjectId === resource.projectId;

  const allowToMove = !!selectedProjectId && !isSameLocation;

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...props}>
      <DialogTitle
        sx={(theme) => ({
          p: theme.spacing(3, 3, 2, 3),
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          gap: 1,
        })}
      >
        Move file: {resource.name}
      </DialogTitle>

      <DialogContent
        dividers
        sx={{
          pt: 1,
          pb: 0,
          border: 'none',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
        }}
      >
        {currentLocationDisplay && (
          <Box display="flex" alignItems="center">
            <Typography variant="caption" color="textSecondary" sx={{ mr: 1 }}>
              Current Location:
            </Typography>
            {currentLocationDisplay}
          </Box>
        )}
        <Typography variant="caption" color="textSecondary" sx={{ mr: 1 }}>
          Destination:
        </Typography>
        <ProjectDropdown
          excludedProjects={resource.projectId ? [resource.projectId] : []}
          onChange={setSelectedProjectId}
          sx={{ mt: 1 }}
        />
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" color="inherit" onClick={onClose}>
          Cancel
        </Button>
        <LoadingButton
          variant="contained"
          disabled={!allowToMove}
          loading={isUpdating}
          onClick={handleMove}
        >
          Move
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};

export default MoveResourceDialog;
