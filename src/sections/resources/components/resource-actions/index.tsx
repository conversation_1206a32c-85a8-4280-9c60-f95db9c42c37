import type { BoxProps } from '@mui/material';

import { toast } from 'sonner';
import FileSaver from 'file-saver';
import { useDispatch, useSelector } from 'react-redux';
import { useBoolean, usePopover } from 'minimal-shared/hooks';

import { Box, Tooltip, Divider, MenuItem, MenuList, Checkbox, IconButton } from '@mui/material';

import useAnalytics from 'src/hooks/analytics';
import useResourcePermissions from 'src/hooks/use-resource-permissions';

import { TranscriptStatus } from 'src/types';
import { toggleSelectResource } from 'src/store/slices/resources/slice';
import { selectSelectedResources } from 'src/store/slices/resources/selectors';

import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

import useDownloadTranscription from 'src/sections/resources/hooks/download-transcription';

import RemoveResourceDialog from './components/remove-dialog';
import RenameResourceDialog from './components/rename-dialog';
import MoveResourceDialog from './components/move-resource-dialog';

import type { ResourceItem } from '../resources-list';

type HiddenActions = 'select' | 'view' | 'download' | 'delete';

interface Props extends Omit<BoxProps, 'resource'> {
  resource: ResourceItem;
  hiddenActions?: HiddenActions[];
  appendActions?: React.ReactNode;
}

const ResourceActions: React.FC<Props> = ({
  resource,
  sx = {},
  hiddenActions = [],
  appendActions,
  ...props
}) => {
  const { trackEvent } = useAnalytics();
  const dispatch = useDispatch();

  const deleteDialog = useBoolean();
  const moveDialog = useBoolean();
  const renameDialog = useBoolean();
  const menuActions = usePopover();

  const selectedResources = useSelector(selectSelectedResources);
  const { canEdit } = useResourcePermissions({ resource });

  const downloadTranscription = useDownloadTranscription();

  const isSelected = selectedResources.some((item) => item.id === resource.id);

  const isSessionCard = resource.cardType === 'session';
  const isTranscriptionReady = resource.transcriptionJobStatus === TranscriptStatus.Completed;
  const isTranscriptionEmpty = !resource.transcription?.length;

  const allowSelect = !isSessionCard && isTranscriptionReady && !hiddenActions.includes('select');

  const onDownloadSource = () => {
    if (!resource.url) return;
    trackEvent({
      eventCategory: 'Resource',
      eventAction: 'Download source',
      properties: {
        resourceId: resource.id,
        resourceName: resource.name,
      },
    });

    FileSaver.saveAs(resource.url, resource.name);
    toast.success(`Download started for ${resource.name}`, {
      description: 'Please wait while the file being downloaded...',
      duration: 50000,
    });
  };

  const onDownloadTranscription = () => {
    if (!resource.transcription?.length) return;
    trackEvent({
      eventCategory: 'Resource',
      eventAction: 'Download transcription',
      properties: {
        resourceId: resource.id,
        resourceName: resource.name,
      },
    });

    downloadTranscription(resource.transcription, `${resource.name}.docx`);
    toast.success(`Downloading transcription for ${resource.name}`, {
      description: 'Please wait while the file being downloaded...',
    });
  };

  const onSelectResource = (evt: React.ChangeEvent<HTMLInputElement>) => {
    evt.stopPropagation();
    dispatch(toggleSelectResource(resource));
  };

  const onOpenMenu = (evt: React.MouseEvent<HTMLButtonElement>) => {
    evt.stopPropagation();
    menuActions.onOpen(evt);
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          ...sx,
        }}
        {...props}
      >
        {appendActions}
        {allowSelect && (
          <Tooltip title="Select file" placement="top" arrow>
            <Checkbox
              sx={{ py: 0 }}
              size="small"
              checked={isSelected}
              onChange={onSelectResource}
            />
          </Tooltip>
        )}
        <Tooltip title="More actions" placement="top" arrow>
          <IconButton disableRipple sx={{ py: 0 }} onClick={onOpenMenu}>
            <Iconify icon="eva:more-vertical-fill" />
          </IconButton>
        </Tooltip>
      </Box>
      <CustomPopover
        open={menuActions.open}
        anchorEl={menuActions.anchorEl}
        onClose={menuActions.onClose}
        slotProps={{ arrow: { placement: 'top-left' } }}
      >
        <MenuList>
          {!isSessionCard && (
            <>
              <MenuItem onClick={renameDialog.onTrue} disabled={!canEdit}>
                <Iconify icon="eva:edit-2-outline" />
                Rename
              </MenuItem>
              <MenuItem onClick={moveDialog.onTrue} disabled={!canEdit}>
                <Iconify icon="eva:move-fill" />
                Move
              </MenuItem>
              <MenuItem onClick={onDownloadSource}>
                <Iconify icon="solar:download-minimalistic-bold" />
                Download source
              </MenuItem>
              {isTranscriptionReady && !isTranscriptionEmpty && (
                <MenuItem onClick={onDownloadTranscription}>
                  <Iconify icon="solar:document-text-broken" />
                  Download transcription
                </MenuItem>
              )}
            </>
          )}
          <Divider />
          <MenuItem
            onClick={deleteDialog.onTrue}
            sx={{
              color: canEdit ? 'error.main' : 'inherit',
            }}
            disabled={!canEdit}
          >
            <Iconify icon="solar:trash-bin-trash-bold" />
            Delete
          </MenuItem>
        </MenuList>
      </CustomPopover>
      {/* Dialogs */}
      <MoveResourceDialog
        open={moveDialog.value}
        onClose={moveDialog.onFalse}
        resource={resource}
      />
      <RemoveResourceDialog
        isSessionCard={isSessionCard}
        open={deleteDialog.value}
        onClose={deleteDialog.onFalse}
        resource={resource}
      />
      <RenameResourceDialog
        open={renameDialog.value}
        onClose={renameDialog.onFalse}
        resource={resource}
      />
    </>
  );
};

export default ResourceActions;
