import { format } from 'date-fns';
import { useDispatch } from 'react-redux';

import { Box, Card, Link, Stack, Button, Typography } from '@mui/material';

import useAnalytics from 'src/hooks/analytics';

import { TranscriptStatus } from 'src/types';
import { viewResource } from 'src/store/slices/resources/slice';

import TruncateTypography from 'src/components/truncate-typography';

import ResourceActions from '../resource-actions';
import ResourceLocation from './components/resource-location';
import ResourceThumbnail from './components/resource-thumbnail';
import useCheckResourceStatus from '../../hooks/check-resource-status';

import type { ResourceCardProps } from '.';

const ResourceSimpleCard: React.FC<ResourceCardProps> = ({
  data,
  hideActions = false,
  hidePreview = false,
  sx = {},
  ...props
}) => {
  const { trackEvent } = useAnalytics();
  const dispatch = useDispatch();
  const isSessionCard = data.cardType === 'session';
  const skipChecking = isSessionCard || data.transcriptionJobStatus === TranscriptStatus.Completed;

  const showHeader = data.title || !hideActions;
  const hasMeetingUrl = isSessionCard && data.meetingUrl;

  // Hook for checking resource status
  useCheckResourceStatus(skipChecking ? undefined : data.id);

  const handleViewResource = () => {
    if (isSessionCard || !data) return;

    dispatch(viewResource(data));
    trackEvent({
      eventCategory: 'Resource',
      eventAction: 'View resource',
      properties: {
        resourceId: data.id,
        resourceName: data.name,
      },
    });
  };

  return (
    <Card
      sx={{
        borderRadius: 1,
        width: '100%',
        p: 2,
        height: '100%',
        ...sx,
      }}
      elevation={2}
      {...props}
    >
      {showHeader && (
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
          <Typography variant="body2" color="textSecondary">
            {data.title ?? ''}
          </Typography>
          {!hideActions && (
            <Stack
              direction="row"
              justifyContent="flex-end"
              alignItems="center"
              sx={{ height: 'fit-content' }}
            >
              <ResourceActions resource={data} />
            </Stack>
          )}
        </Stack>
      )}
      {!hidePreview && (
        <Box onClick={handleViewResource} sx={{ cursor: 'pointer' }}>
          <ResourceThumbnail data={data} />
        </Box>
      )}

      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="flex-end"
        sx={{ mt: 1.5, width: '100%' }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', gap: 1 }}>
          <TruncateTypography
            variant="subtitle1"
            sx={{
              fontSize: 16,
              cursor: 'pointer',
              '&:hover': { textDecoration: 'underline', textUnderlineOffset: 3 },
            }}
            text={data.name}
            maxLength={30}
            onClick={handleViewResource}
          />
          <Typography variant="caption" color="textSecondary">
            Creation date: {format(data.fileLastModified, 'dd MMM yyyy hh:mm a')}
          </Typography>
          <ResourceLocation resource={data} />
        </Box>
        {hasMeetingUrl && (
          <Link href={data.meetingUrl} target="_blank" rel="noopener noreferrer">
            <Button variant="contained" color="primary" size="small" sx={{ minWidth: '100px' }}>
              Join Meeting
            </Button>
          </Link>
        )}
      </Stack>
    </Card>
  );
};

export default ResourceSimpleCard;
