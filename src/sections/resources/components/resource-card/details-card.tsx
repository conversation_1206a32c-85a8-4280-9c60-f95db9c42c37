import { format } from 'date-fns';
import { useDispatch } from 'react-redux';
import { useMemo, useState, useEffect } from 'react';

import {
  Box,
  Tab,
  Card,
  Tabs,
  Stack,
  Divider,
  useTheme,
  Typography,
  IconButton,
  useMediaQuery,
  CircularProgress,
} from '@mui/material';

import useAiAgent from 'src/hooks/ai-agent';

import { TranscriptStatus } from 'src/types';
import { viewResource } from 'src/store/slices/resources/slice';
import { useLazyGetResourceQuery } from 'src/store/api/resources';

import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import TruncateTypography from 'src/components/truncate-typography';

import ChatBox from 'src/sections/chat/chat-box';

import ResourceActions from '../resource-actions';
import { NotesSection } from './components/notes-section';
import ResourceLocation from './components/resource-location';
import ResourcePreviewer from './components/resource-previewer';
import TranscriptionSection from './components/transcription-section';

import type { ResourceCardProps } from '.';

enum ResourceTabs {
  Transcription = 'transcription',
  Notes = 'notes',
}

const ResourceDetailsCard: React.FC<ResourceCardProps> = ({ data, sx, ...props }) => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [activeTab, setActiveTab] = useState<ResourceTabs>();
  const {
    messages,
    isReplying,
    allowSendMessage,
    sendMessage,
    isStartingConversation,
    startConversationForResource,
  } = useAiAgent();

  const [getResource, { data: resource, isLoading }] = useLazyGetResourceQuery({
    refetchOnFocus: false,
  });

  useEffect(() => {
    if (!data.id) return;
    getResource({ id: data.id });
  }, [data.id]);

  const isTranscriptionReady = useMemo(
    () => resource?.transcriptionJobStatus === TranscriptStatus.Completed,
    [resource?.transcriptionJobStatus]
  );

  const isResourceHasNotes = useMemo(
    () => resource?.userPermissions?.canEdit ?? false,
    [resource?.userPermissions]
  );

  useEffect(() => {
    if (!isTranscriptionReady || !resource) return;

    startConversationForResource(resource);
  }, [isTranscriptionReady, resource]);

  const tabs = useMemo(() => {
    const fFormat = fileFormat(data?.fileName);
    const isAudioVideoFile = fFormat === 'audio' || fFormat === 'video';
    return [
      ...(isAudioVideoFile
        ? [
            {
              label: 'Transcript',
              value: ResourceTabs.Transcription,
            },
          ]
        : []),
      {
        label: 'Notes',
        value: ResourceTabs.Notes,
      },
    ];
  }, [data]);

  useEffect(() => {
    if (tabs.length > 0) {
      setActiveTab(tabs[0].value);
    }
  }, [tabs]);

  const onClose = () => {
    dispatch(viewResource(null));
  };

  return (
    <Card
      sx={{
        borderRadius: 1,
        width: '100%',
        p: 1,
        height: '100%',
        overflow: 'hidden',
        ...(Array.isArray(sx) ? sx : [sx]),
      }}
      className="scrollbar"
      elevation={2}
      {...props}
    >
      {isLoading || !resource ? (
        <Box
          sx={{
            my: 3,
            display: 'flex',
            justifyContent: isLoading ? 'center' : 'flex-start',
            alignItems: 'center',
          }}
        >
          <CircularProgress size="3rem" />
        </Box>
      ) : (
        <Stack direction={isMobile ? 'column' : 'row'} sx={{ gap: 2, height: '100%' }}>
          <Stack
            className="scrollbar"
            sx={{ width: isMobile ? '100%' : '40%', direction: 'column', flexShrink: 0 }}
          >
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="baseline"
              sx={{ p: 1 }}
            >
              <Stack direction="column" alignItems="flex-start" gap={1}>
                <TruncateTypography
                  variant="subtitle1"
                  sx={{ fontSize: 16 }}
                  text={data.name}
                  maxLength={30}
                />
                <Typography variant="caption" color="textSecondary">
                  Creation date: {format(resource.fileLastModified, 'dd MMM yyyy hh:mm a')}
                </Typography>
                <ResourceLocation resource={data} />
              </Stack>
              <Stack direction="row" gap={2}>
                <ResourceActions resource={resource} sx={{ maxHeight: 20 }} />
                <IconButton color="default" edge="start" onClick={onClose} sx={{ p: 0 }}>
                  <Iconify icon="mingcute:close-line" />
                </IconButton>
              </Stack>
            </Stack>
            <Stack direction="column" sx={{ gap: 1 }}>
              <Box sx={{ width: isMobile ? '100%' : 'auto', p: 1 }}>
                <ResourcePreviewer data={resource} />
              </Box>

              {tabs.length > 0 ? (
                <Stack direction="column" sx={{ gap: 1, height: '100%' }}>
                  <Box sx={{ borderBottom: 1, borderColor: 'divider', px: 1 }}>
                    {tabs.length > 1 ? (
                      <Tabs
                        value={activeTab}
                        onChange={(e, newValue) => setActiveTab(newValue)}
                        variant="scrollable"
                        scrollButtons="auto"
                        sx={{
                          '& .MuiTabs-flexContainer': {
                            display: 'flex',
                            justifyContent: 'space-around',
                          },
                        }}
                      >
                        {tabs.map((tab) => (
                          <Tab sx={{ fontSize: '18px' }} label={tab.label} value={tab.value} />
                        ))}
                      </Tabs>
                    ) : (
                      <Typography variant="subtitle1" sx={{ p: 1, pt: 1.5 }}>
                        Notes
                      </Typography>
                    )}
                  </Box>
                  {activeTab === 'notes' && (
                    <NotesSection resourceId={data.id} isResourceHasNotes={isResourceHasNotes} />
                  )}
                  {activeTab === 'transcription' && (
                    <TranscriptionSection
                      status={resource.transcriptionJobStatus ?? TranscriptStatus.InProgress}
                      data={resource.transcription ?? []}
                      sx={{ p: 1 }}
                    />
                  )}
                </Stack>
              ) : null}
            </Stack>
          </Stack>
          <Divider orientation={isMobile ? 'horizontal' : 'vertical'} flexItem />
          <Stack sx={{ width: '100%', direction: 'column', height: '100%' }}>
            <Typography variant="subtitle1" sx={{ p: 1, pt: 1.5 }}>
              Aida Chat
            </Typography>
            <ChatBox
              containerSx={{
                display: 'flex',
                flexDirection: 'column',
                height: 'auto',
              }}
              messageListSx={{ height: isMobile ? 400 : '65vh', pt: 2, px: 0, pr: 1 }}
              messages={messages}
              loading={isStartingConversation}
              isReplying={isReplying}
              disabled={!allowSendMessage}
              sendMessage={sendMessage}
            />
          </Stack>
        </Stack>
      )}
    </Card>
  );
};

export default ResourceDetailsCard;
