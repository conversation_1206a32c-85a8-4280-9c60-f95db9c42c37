import { CONFIG } from 'src/global-config';

import { fileFormat } from 'src/components/file-thumbnail';
import { EmptyContent } from 'src/components/empty-content';

import ResourcePdfPreviewer from './pdf';
import ResourceTextPreviewer from './text';
import ResourceVideoPreviewer from './video';
import ResourceAudioPreviewer from './audio';

import type { ResourceItem } from '../../../resources-list';

const PreviewerComponentsMap: Record<string, React.FC<{ data: ResourceItem }>> = {
  video: ResourceVideoPreviewer,
  audio: ResourceAudioPreviewer,
  pdf: ResourcePdfPreviewer,
  txt: ResourceTextPreviewer,
};

const ResourcePreviewer: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const format = fileFormat(data.fileName || data.name || '');

  const PreviewerComponent = PreviewerComponentsMap[format];

  if (!PreviewerComponent) {
    return (
      <EmptyContent
        title="Preview is unavailable for this file type"
        description="Please download the file to view it"
        imgUrl={`${CONFIG.assetsDir}/assets/icons/empty/ic-content.svg`}
      />
    );
  }

  return <PreviewerComponent data={data} />;
};

export default ResourcePreviewer;
