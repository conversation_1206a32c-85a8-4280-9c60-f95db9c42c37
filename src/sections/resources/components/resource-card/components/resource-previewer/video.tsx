import type { Resource } from 'src/types';

import { toast } from 'sonner';
import { useRef, useState } from 'react';

import { CardCover } from '@mui/joy';
import LoadingButton from '@mui/lab/LoadingButton';
import { Box, Card, Tooltip } from '@mui/material';

import { useUploadResourceThumbnailMutation } from 'src/store/api/resources';

import { Image } from 'src/components/image';

const ResourceVideoPreviewer: React.FC<{ data: Resource }> = ({ data }) => {
  const [uploadResourceThumbnail, { isLoading }] = useUploadResourceThumbnailMutation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoError, setVideoError] = useState(false);
  const [isVideoLoading, setIsVideoLoading] = useState(true);

  const generateThumbnail = () => {
    if (!videoRef.current) return null;

    // Pause video to capture frame
    videoRef.current.pause();

    // Handle generate thumbnail from frame
    const canvas = document.createElement('canvas');
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;

    const ctx = canvas.getContext('2d');
    if (!ctx) return null;

    ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
    const imageUrl = canvas.toDataURL('image/png');

    return imageUrl;
  };

  const handleCaptureFrame = async () => {
    const imageUrl = generateThumbnail();
    if (!imageUrl) return;

    try {
      await uploadResourceThumbnail({ id: data.id, payload: { imageUrl } }).unwrap();
      toast.success('Thumbnail uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload thumbnail');
    }
  };

  const handleVideoError = () => {
    console.error('Video failed to load:', data.url);
    setVideoError(true);
    setIsVideoLoading(false);
  };

  const handleVideoLoad = () => {
    setVideoError(false);
    setIsVideoLoading(false);
  };

  // If no URL or video failed to load, show thumbnail fallback
  if (!data.url || videoError) {
    return (
      <Box
        sx={{
          width: '100%',
          aspectRatio: '16/9',
          borderRadius: 1,
          overflow: 'hidden',
        }}
      >
        <Image
          src={data.thumbnailUrl}
          alt={data.name}
          ratio="16/9"
          sx={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
      </Box>
    );
  }

  return (
    <Card
      sx={{
        width: '100%',
        aspectRatio: '16/9',
        position: 'relative',
        borderRadius: 1,
        overflow: 'hidden',
      }}
    >
      <Tooltip arrow title="Choose this frame as resource thumbnail">
        <LoadingButton
          loading={isLoading}
          variant="contained"
          size="small"
          color="primary"
          sx={{
            position: 'absolute',
            top: 8,
            left: 8,
            zIndex: 10,
          }}
          onClick={handleCaptureFrame}
          disabled={videoError || isVideoLoading}
        >
          Capture as thumbnail
        </LoadingButton>
      </Tooltip>
      <CardCover>
        <video
          ref={videoRef}
          controls
          poster={data.thumbnailUrl}
          crossOrigin="anonymous"
          onError={handleVideoError}
          onLoadedData={handleVideoLoad}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        >
          <source src={data.url} type="video/mp4" />
        </video>
      </CardCover>
    </Card>
  );
};

export default ResourceVideoPreviewer;
