import { useState, useEffect } from 'react';

import { Box, Typography } from '@mui/material';

import { Markdown } from 'src/components/markdown';
import { Scrollbar } from 'src/components/scrollbar';
import { LoadingScreen } from 'src/components/loading-screen';

import type { ResourceItem } from '../../../resources-list';

const ResourceTextPreviewer: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const [texts, setTexts] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!data.url) {
      setLoading(false);
      setError('Text file URL not available. The file may still be processing.');
      return;
    }

    setLoading(true);
    setError(null);

    fetch(data.url)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
        }
        return response.text();
      })
      .then((text) => {
        setTexts(text);
        setError(null);
      })
      .catch((err) => {
        console.error('Failed to load text file:', data.url, err);
        setError('Failed to load text file. The file may not be ready yet or the URL is invalid.');
      })
      .finally(() => setLoading(false));
  }, [data.url]);

  if (loading) return <LoadingScreen />;

  if (error) {
    return (
      <Box
        sx={{
          height: 500,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 3,
        }}
      >
        <Typography variant="body2" color="error" textAlign="center">
          {error}
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          Please try refreshing the page or check back later.
        </Typography>
      </Box>
    );
  }

  return (
    <Scrollbar
      sx={{
        height: 500,
      }}
    >
      <Markdown
        sx={{
          whiteSpace: 'pre-wrap',
          wordWrap: 'break-word',
          overflowWrap: 'break-word',
        }}
        children={texts}
      />
    </Scrollbar>
  );
};

export default ResourceTextPreviewer;
