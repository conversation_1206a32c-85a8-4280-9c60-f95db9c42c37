import { useRef, useState } from 'react';
import { useResizeObserver } from 'usehooks-ts';
import { Page, pdfjs, Document } from 'react-pdf';

import { Box, Fade, Typography } from '@mui/material';

import { LoadingScreen } from 'src/components/loading-screen';

import { THUMBNAIL_HEIGHT } from '.';

import type { ResourceItem } from '../../../resources-list';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url
).toString();

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
};

const ResourcePdfThumbnail: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>();
  const [isLoading, setIsLoading] = useState(true);
  const [pdfError, setPdfError] = useState(false);

  useResizeObserver({
    ref: containerRef,
    onResize: (size) => setContainerWidth(size.width),
  });

  const handleLoadSuccess = () => {
    setIsLoading(false);
    setPdfError(false);
  };

  const handleLoadError = (error: any) => {
    console.error('PDF thumbnail failed to load:', data.url, error);
    setIsLoading(false);
    setPdfError(true);
  };

  // If no URL or PDF failed to load, show fallback
  if (!data.url || pdfError) {
    return (
      <Box
        sx={{
          width: '100%',
          height: THUMBNAIL_HEIGHT,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          border: '1px dashed',
          borderColor: 'divider',
          borderRadius: 1,
        }}
      >
        <Typography variant="body2" color="text.secondary" textAlign="center">
          {!data.url ? 'PDF preview not available yet' : 'PDF preview failed to load'}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        height: THUMBNAIL_HEIGHT,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        '& .react-pdf__Page': {
          display: 'flex',
          justifyContent: 'center',
          '& canvas': {
            maxWidth: '100%',
            maxHeight: THUMBNAIL_HEIGHT,
            height: 'auto !important',
            width: 'auto !important',
          },
        },
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            zIndex: 1,
          }}
        >
          <LoadingScreen />
        </Box>
      )}
      <Fade in={!isLoading} timeout={300}>
        <Box>
          <Document
            loading={null}
            file={data.url}
            onLoadSuccess={handleLoadSuccess}
            onLoadError={handleLoadError}
            options={options}
          >
            <Page
              pageNumber={1}
              width={containerWidth}
              renderTextLayer={false}
              renderAnnotationLayer={false}
              loading={null}
            />
          </Document>
        </Box>
      </Fade>
    </Box>
  );
};

export default ResourcePdfThumbnail;
