import { Box } from '@mui/material';

import { Image } from 'src/components/image';

import { THUMBNAIL_HEIGHT } from '.';

import type { ResourceItem } from '../../../resources-list';

const ResourceVideoThumbnail: React.FC<{ data: ResourceItem }> = ({ data }) => (
  <Box
    sx={{
      height: THUMBNAIL_HEIGHT,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
  >
    <Image
      visibleByDefault
      src={data.thumbnailUrl}
      alt={data.name}
      sx={{
        height: '100%',
        borderRadius: 1,
        '& .minimal__image__img': { objectFit: 'cover' },
      }}
    />
  </Box>
);

export default ResourceVideoThumbnail;
