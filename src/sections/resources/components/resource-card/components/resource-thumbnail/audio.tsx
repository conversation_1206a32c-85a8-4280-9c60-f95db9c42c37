import { useRef } from 'react';
import { useWavesurfer } from '@wavesurfer/react';

import { Box, Chip, useTheme } from '@mui/material';

import { fSecsToTime } from 'src/utils/format-time';

import type { ResourceItem } from '../../../resources-list';

const ResourceAudioThumbnail: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const theme = useTheme();
  const containerRef = useRef(null);

  useWavesurfer({
    container: containerRef,
    url: data.url,
    waveColor: theme.palette.primary.main,
    barWidth: 2,
    barGap: 5,
    barRadius: 2,
    height: 200,
  });

  return (
    <Box
      sx={{
        pointerEvents: 'none',
        py: 3,
        position: 'relative',
      }}
    >
      <div ref={containerRef} />
      <Chip
        variant="outlined"
        color="default"
        size="small"
        label={fSecsToTime(data.duration)}
        sx={{ position: 'absolute', bottom: 0, right: theme.spacing(2) }}
      />
    </Box>
  );
};

export default ResourceAudioThumbnail;
