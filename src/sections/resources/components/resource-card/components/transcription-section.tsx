import type { SxProps } from '@mui/material';
import type { Transcription } from 'src/types';

import { useMemo } from 'react';

import { Box, Chip, Stack, Typography } from '@mui/material';

import { fSecsToTime } from 'src/utils/format-time';

import { TranscriptStatus } from 'src/types';

import { Scrollbar } from 'src/components/scrollbar';

const TranscriptionSection: React.FC<{
  status: TranscriptStatus;
  data: Transcription[];
  sx?: SxProps;
}> = ({ status, data, sx = {} }) => {
  const transcriptionDisplay = useMemo(() => {
    if (status === TranscriptStatus.InProgress) {
      return <Typography variant="h6">Transcription is being generated...</Typography>;
    }
    if (status === TranscriptStatus.Failed) {
      return <Typography variant="h6">Failed to generate transcription</Typography>;
    }

    if (!data.length) return null;

    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, ...sx }}>
        {(data ?? []).map((item) => (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, pr: 2 }}>
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Typography variant="subtitle2">{item.nameFromRevAi}:</Typography>
              <Chip
                variant="outlined"
                color="default"
                size="small"
                label={fSecsToTime(item.startTime)}
              />
            </Stack>
            <Typography variant="body2">{item.content}</Typography>
          </Box>
        ))}
      </Box>
    );
  }, [data, status]);

  if (!transcriptionDisplay) return null;

  return <Scrollbar sx={{ height: 300, mt: 1 }}>{transcriptionDisplay}</Scrollbar>;
};

export default TranscriptionSection;
