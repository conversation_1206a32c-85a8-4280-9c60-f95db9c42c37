import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';

import { useMemo } from 'react';

import { Link, Button } from '@mui/material';
import FolderRoundedIcon from '@mui/icons-material/FolderRounded';

import { paths } from 'src/routes/paths';

const ResourceLocation: React.FC<{ resource: Resource; sx?: SxProps }> = ({ resource, sx }) => {
  const location = useMemo(() => {
    if (!resource.project) return null;
    // If the resource is in a folder, return the folder name and url
    if (resource.folder) {
      return {
        name: resource.folder.name,
        url: paths.project.folder(resource?.project?.id ?? '', resource?.folder?.id ?? ''),
      };
    }
    // Otherwise, fallback to project
    return {
      name: resource.project.name,
      url: paths.project.details(resource?.project?.id ?? ''),
    };
  }, [resource.project, resource.folder]);

  if (!location) return null;

  return (
    <Link
      href={location.url}
      target="_self"
      sx={{ width: 'fit-content', color: 'text.primary', ...sx }}
    >
      <Button
        variant="outlined"
        size="small"
        startIcon={<FolderRoundedIcon fontSize="medium" color="action" />}
      >
        {location.name}
      </Button>
    </Link>
  );
};

export default ResourceLocation;
