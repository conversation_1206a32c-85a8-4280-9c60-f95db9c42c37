import type { Resource } from 'src/types';
import type { BoxProps } from '@mui/material';

import { capitalize } from 'es-toolkit';

import { Box, Chip } from '@mui/material';

const ResourceTopics: React.FC<{ data: Resource } & BoxProps> = ({ data, sx = {}, ...props }) => {
  const { topics } = data;
  if (!topics || !topics.length) {
    return null;
  }
  return (
    <Box sx={{ display: 'flex', flexDirection: 'row', gap: 1, ...sx }} {...props}>
      {topics.map((topic) => (
        <Chip variant="outlined" color="primary" label={capitalize(topic.topic_name)} />
      ))}
    </Box>
  );
};

export default ResourceTopics;
