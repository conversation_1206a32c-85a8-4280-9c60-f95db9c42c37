import type { Note } from 'src/store/api/notes/types';

import { toast } from 'sonner';
import { useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';

import LoadingButton from '@mui/lab/LoadingButton';

import { useAppSelector } from 'src/store';
import { selectSelectedNote } from 'src/store/slices/notes/selectors';
import { addNote, deleteNote, selectNote, storeNotes } from 'src/store/slices/notes/slice';
import {
  useCreateNoteMutation,
  useDeleteNoteMutation,
  useGetNotesByResourceQuery,
} from 'src/store/api/notes';

import { NoteDialog } from 'src/components/notes';
import { Scrollbar } from 'src/components/scrollbar';
import { NoteList } from 'src/components/notes/note-list';
import { LoadingScreen } from 'src/components/loading-screen';

interface NotesSectionProps {
  resourceId: string;
  isResourceHasNotes: boolean;
  onClose?: () => void;
  onMaximize?: () => void;
}

export function NotesSection({
  resourceId,
  isResourceHasNotes,
  onClose,
  onMaximize,
}: NotesSectionProps) {
  const dispatch = useDispatch();
  const selectedNote = useAppSelector(selectSelectedNote);

  const [createNote, { isLoading: isCreating }] = useCreateNoteMutation();
  const [deleteNoteMutation, { isLoading: isDeleting }] = useDeleteNoteMutation();
  const {
    data: apiNotes = [],
    isLoading: isApiLoading,
    refetch,
  } = useGetNotesByResourceQuery(
    { resourceId },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  useEffect(() => {
    if (!isApiLoading && apiNotes) {
      dispatch(storeNotes(apiNotes));
    }
  }, [dispatch, isApiLoading, apiNotes]);

  const handleSelectNote = (note: Note) => {
    dispatch(selectNote(note));
  };

  const handleCloseDialog = () => {
    refetch();
    dispatch(selectNote(null));
  };

  const handleAddNewNote = useCallback(() => {
    createNote({
      payload: {
        content: 'Edit this note',
        title: 'New Note',
        resourceId,
      },
    })
      .then((res) => {
        if (res.data) {
          handleSelectNote(res.data);
          dispatch(addNote(res.data));
        }
      })
      .catch((err) => {
        toast.error('Failed to add new note');
      });
  }, [createNote, resourceId, handleSelectNote]);

  const handleDelete = async (noteId: string) => {
    try {
      await deleteNoteMutation({ id: noteId });
      dispatch(deleteNote(noteId));
      await refetch();
      toast.success('Note deleted successfully');
    } catch (error) {
      toast.error('Failed to delete note');
    }
  };

  if (isApiLoading) {
    return <LoadingScreen />;
  }

  return (
    <Scrollbar sx={{ height: 300, mt: 1, p: 2 }}>
      <NoteList
        notes={apiNotes}
        onSelectNote={handleSelectNote}
        onDelete={handleDelete}
        isCanDeleteResource={isResourceHasNotes}
      />
      {isResourceHasNotes && (
        <LoadingButton
          sx={{ mt: 2 }}
          variant="contained"
          color="primary"
          onClick={handleAddNewNote}
          loading={isCreating}
        >
          Add Note
        </LoadingButton>
      )}

      <NoteDialog
        open={Boolean(selectedNote)}
        onClose={handleCloseDialog}
        isCanEdit={isResourceHasNotes}
      />
    </Scrollbar>
  );
}
