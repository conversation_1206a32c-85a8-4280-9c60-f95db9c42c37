import type { CardProps } from '@mui/material';

import ResourceSimpleCard from './simple-card';
import ResourceDetailsCard from './details-card';

import type { ResourceItem } from '../resources-list';

export interface ResourceCardProps extends CardProps {
  data: ResourceItem;
  view?: 'simple' | 'details';
  hideActions?: boolean;
  hidePreview?: boolean;
}

const ResourceCard: React.FC<ResourceCardProps> = ({ view = 'simple', ...props }) => {
  if (view === 'details') return <ResourceDetailsCard {...props} />;
  return <ResourceSimpleCard {...props} />;
};

export default ResourceCard;
