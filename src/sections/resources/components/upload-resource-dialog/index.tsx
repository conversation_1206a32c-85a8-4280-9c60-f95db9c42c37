import type { DialogProps } from '@mui/material/Dialog';

import { toast } from 'sonner';
import { useMemo, useState, useEffect } from 'react';

import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import { Box, Button, Typography } from '@mui/material';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useUploadFile } from 'src/utils/resource';

import { Upload } from 'src/components/upload';
import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import { BaseOption } from 'src/components/settings/drawer/base-option';

import ProjectDropdown from 'src/sections/projects/components/project-dropdown';

// ----------------------------------------------------------------------

type Props = DialogProps & {
  open: boolean;
  title?: string;
  onClose: () => void;
  projectId?: string;
};

const MAX_FILE_SIZE = 5120 * 1024 * 1024; // 5GB

// TODO: Support docx later
const ACCEPTED_FILE_FORMATS = ['audio', 'video', 'pdf', 'txt', 'md'];
const UNSUPPORTED_VIDEO_TYPES = ['video/x-matroska'];

const UploadResourceDialog: React.FC<Props> = ({
  open,
  onClose,
  title = 'Upload files',
  projectId,
  ...other
}) => {
  const uploadFile = useUploadFile();

  const [files, setFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string>(projectId || '');
  const [cardOption, setCardOption] = useState<'delete' | 'keep'>('keep');

  useEffect(() => {
    if (!open) {
      setFiles([]);
    }
  }, [open]);

  const isWarningUnsupportedVideo = useMemo(
    () => files.some((file) => UNSUPPORTED_VIDEO_TYPES.includes(file.type)),
    [files]
  );

  const handleDrop = (acceptedFiles: File[]) => {
    // Only allow audio and video files
    const filteredFiles = acceptedFiles.filter((file) => {
      const format = fileFormat(file.name);
      return ACCEPTED_FILE_FORMATS.includes(format);
    });

    setFiles((prevFiles) => [...prevFiles, ...filteredFiles]);
  };

  const handleRemoveFile = (inputFile: File | string) => {
    const filtered = files.filter((file) => file !== inputFile);
    setFiles(filtered);
  };

  const handleRemoveAllFiles = () => {
    setFiles([]);
  };

  const handleUpload = async () => {
    if (!files.length) return;
    try {
      setIsSubmitting(true);
      await Promise.all(
        files.map((file) => {
          if (UNSUPPORTED_VIDEO_TYPES.includes(file.type)) {
            return uploadFile(file, selectedProjectId, undefined, {
              transcoded: true,
              mode: cardOption === 'keep' ? 'keep-both' : 'delete-original',
            });
          }
          return uploadFile(file, selectedProjectId);
        })
      );
      toast.success('Files submitted. Uploading in progress...');
      onClose();
    } catch (error: any) {
      toast.error('Something wrong! Unable to submit files', {
        description: error.message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderUnsupportedVideoWarning = () => {
    if (!isWarningUnsupportedVideo) return null;

    return (
      <Box>
        <Typography variant="body2">
          If the file is a video, you can choose to delete the original file and keep only the
          transcoded resource.
        </Typography>
        <div style={{ display: 'flex', gap: 16, flexDirection: 'row', marginTop: 16 }}>
          <BaseOption
            icon="file"
            label="Keep both original and transcoded resources."
            selected={cardOption === 'keep'}
            onChangeOption={() => setCardOption('keep')}
          />
          <BaseOption
            icon="delete"
            label="Delete original file and keep only transcoded resource."
            selected={cardOption === 'delete'}
            onChangeOption={() => setCardOption('delete')}
          />
        </div>
      </Box>
    );
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...other}>
      <DialogTitle sx={[(theme) => ({ p: theme.spacing(3, 3, 2, 3) })]}>{title}</DialogTitle>

      <DialogContent
        dividers
        sx={{
          pt: 1,
          pb: 0,
          border: 'none',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 1,
        }}
      >
        <ProjectDropdown
          projectId={selectedProjectId}
          onChange={setSelectedProjectId}
          sx={{ mb: 2 }}
        />
        <Upload
          multiple
          value={files}
          onDrop={handleDrop}
          onRemove={handleRemoveFile}
          maxSize={MAX_FILE_SIZE}
          helperText="PDF, TXT, MD, MP3, M4A, WAV, AAC, OGG, FLAC, WMA, MP4, MOV, AVI, MPG, WEBM, WMV, FLV, MKV"
        />
        {renderUnsupportedVideoWarning()}
      </DialogContent>

      <DialogActions>
        <LoadingButton
          variant="contained"
          startIcon={<Iconify icon="eva:cloud-upload-fill" />}
          onClick={handleUpload}
          disabled={!files.length}
          loading={isSubmitting}
        >
          Upload
        </LoadingButton>
        {!!files.length && (
          <Button variant="outlined" color="inherit" onClick={handleRemoveAllFiles}>
            Remove all
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default UploadResourceDialog;
