import type { SxProps } from '@mui/material';

import { useMemo } from 'react';

import {
  Box,
  Chip,
  Stack,
  Select,
  MenuItem,
  IconButton,
  InputLabel,
  Typography,
  FormControl,
  CircularProgress,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface Props {
  audioDevices: MediaDeviceInfo[];
  selectedAudioDeviceId: string;
  onDeviceChange: (deviceId: string) => void;
  connectionState: 'disconnected' | 'connecting' | 'connected' | 'error';
  isRecording: boolean;
  isConnected: boolean;
  error: string | null;
  onStartRecording: () => void;
  onStopRecording: () => void;
  sx?: SxProps;
}

const AudioDeviceSelector: React.FC<Props> = ({
  audioDevices,
  selectedAudioDeviceId,
  onDeviceChange,
  connectionState,
  isRecording,
  isConnected,
  error,
  onStartRecording,
  onStopRecording,
  sx = {},
}) => {
  // Memoize filtered audio devices for better performance
  const audioInputDevices = useMemo(
    () => audioDevices.filter((device) => device.kind === 'audioinput'),
    [audioDevices]
  );

  const getDeviceStatus = () => {
    if (isRecording) return { color: 'success', text: 'Recording' };
    if (isConnected) return { color: 'info', text: 'Connected' };
    if (connectionState === 'connecting') return { color: 'warning', text: 'Connecting...' };
    if (error) return { color: 'error', text: 'Error' };
    return { color: 'default', text: 'Ready' };
  };

  const deviceStatus = getDeviceStatus();

  const renderStatus = () => {
    if (isRecording) {
      return (
        <Box sx={{ p: 2, bgcolor: 'success.lighter', borderRadius: 1 }}>
          <Typography variant="body2" color="success.main">
            Recording in progress...
          </Typography>
          <IconButton onClick={onStopRecording} sx={{ mt: 1, ml: 1 }}>
            <Iconify icon="eva:close-fill" width={24} />
          </IconButton>
        </Box>
      );
    }
    if (isConnected) {
      return (
        <Box sx={{ p: 2, bgcolor: 'info.lighter', borderRadius: 1 }}>
          <Typography variant="body2" color="info.main">
            Connected to transcription service
          </Typography>
        </Box>
      );
    }
    if (connectionState === 'connecting') {
      return (
        <Box sx={{ p: 2, bgcolor: 'warning.lighter', borderRadius: 1 }}>
          <Typography variant="body2" color="warning.main">
            Connecting to transcription service...
          </Typography>
        </Box>
      );
    }
    if (error) {
      return (
        <Box sx={{ p: 2, bgcolor: 'error.lighter', borderRadius: 1 }}>
          <Typography variant="body2" color="error.main">
            Error connecting to transcription service
          </Typography>
        </Box>
      );
    }
    return (
      <Box sx={{ p: 2, bgcolor: 'default.lighter', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Ready to start recording
        </Typography>
        <IconButton onClick={onStartRecording} sx={{ mt: 1 }}>
          <Iconify icon="material-symbols:mic" width={24} />
        </IconButton>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 1,
        p: 4,
        position: 'relative',
        ...sx,
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          transform: 'translateY(-50%)',
          bgcolor: 'background.paper',
          zIndex: 1,
          px: 2,
          py: 1,
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 4,
        }}
      >
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify
            icon="material-symbols:mic-outline"
            width={16}
            color={isRecording ? 'success.main' : 'text.secondary'}
          />
          <Typography variant="caption" color="text.secondary">
            Audio Device Setup
          </Typography>
          {connectionState === 'connecting' && <CircularProgress size={12} />}
          <Chip
            label={deviceStatus.text}
            color={deviceStatus.color as any}
            size="small"
            variant="outlined"
          />
        </Stack>
      </Box>

      <Stack spacing={3}>
        <Stack spacing={1}>
          <Typography variant="h6" sx={{ fontSize: 16 }}>
            Select Microphone
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {isRecording
              ? 'You can switch devices during recording - audio will reconnect automatically'
              : 'Choose your preferred audio input device'}
          </Typography>
        </Stack>

        <FormControl fullWidth size="small">
          <InputLabel id="select-microphone-label">Microphone</InputLabel>
          <Select
            labelId="select-microphone-label"
            label="Microphone"
            value={selectedAudioDeviceId || ''}
            onChange={(e) => onDeviceChange(e.target.value)}
            disabled={false} // Allow switching during recording since it's now supported
          >
            {audioInputDevices.map((device) => (
              <MenuItem key={device.deviceId} value={device.deviceId}>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ width: '100%' }}>
                  <Iconify icon="material-symbols:mic-outline" width={16} />
                  <Typography variant="body2" noWrap sx={{ flex: 1 }}>
                    {device.label || `Microphone ${device.deviceId.slice(0, 8)}...`}
                  </Typography>
                  {device.deviceId === selectedAudioDeviceId && (
                    <Iconify icon="material-symbols:check" width={16} color="success.main" />
                  )}
                </Stack>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {audioInputDevices.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 2 }}>
            <Iconify
              icon="material-symbols:mic-off"
              width={32}
              color="text.disabled"
              sx={{ mb: 1 }}
            />
            <Typography variant="body2" color="text.secondary">
              No audio devices found
            </Typography>
            <Typography variant="caption" color="text.disabled">
              Please connect a microphone and grant permission
            </Typography>
          </Box>
        )}
        {renderStatus()}
      </Stack>
    </Box>
  );
};

export default AudioDeviceSelector;
