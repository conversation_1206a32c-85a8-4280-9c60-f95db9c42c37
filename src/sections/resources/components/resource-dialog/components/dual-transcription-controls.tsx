import type { SxProps } from '@mui/material';
import type { DualStreamState } from 'src/types';

import { Box, Chip, Stack, Button, Switch, Typography, FormControlLabel } from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface Props {
  isDualMode: boolean;
  isRecording: boolean;
  dualStream: DualStreamState | undefined;
  onToggleDualMode: (enabled: boolean) => void;
  onStartScreenCapture: () => void;
  onStopScreenCapture: () => void;
  sx?: SxProps;
}

const DualTranscriptionControls: React.FC<Props> = ({
  isDualMode,
  isRecording,
  dualStream,
  onToggleDualMode,
  onStartScreenCapture,
  onStopScreenCapture,
  sx = {},
}) => (
  <Box
    sx={{
      border: '1px solid',
      borderColor: 'divider',
      borderRadius: 1,
      p: 3,
      position: 'relative',
      ...sx,
    }}
  >
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        transform: 'translateY(-50%)',
        bgcolor: 'background.paper',
        zIndex: 1,
        px: 2,
        py: 1,
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 4,
      }}
    >
      <Stack direction="row" alignItems="center" spacing={1}>
        <Iconify
          icon="material-symbols:screen-share"
          width={16}
          color={isDualMode ? 'success.main' : 'text.secondary'}
        />
        <Typography variant="caption" color="text.secondary">
          Dual Audio Transcription
        </Typography>
      </Stack>
    </Box>

    <Stack spacing={3}>
      <Stack spacing={1}>
        <Typography variant="h6" sx={{ fontSize: 16 }}>
          Dual Stream Mode
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Capture both speaker audio (via screen sharing) and microphone input simultaneously
        </Typography>
        <Typography variant="caption" color="warning.main" sx={{ fontStyle: 'italic' }}>
          Note: For speaker audio, you must select &lsquo;Share tab audio&rsquo; or &lsquo;Share
          system audio&rsquo; when prompted by your browser.
        </Typography>
      </Stack>

      <Stack spacing={2}>
        {/* Dual Mode Toggle */}
        <Stack direction="row" alignItems="center" spacing={2}>
          <FormControlLabel
            control={
              <Switch
                checked={isDualMode || false}
                onChange={(e) => onToggleDualMode(e.target.checked)}
                disabled={isRecording && !isDualMode}
              />
            }
            label="Enable Dual Transcription"
          />
        </Stack>

        {/* Screen Capture Controls */}
        {isDualMode && (
          <Stack spacing={2}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Iconify icon="material-symbols:screen-share" width={20} />
              <Typography variant="body2">Speaker Audio (Screen Share)</Typography>
              <Chip
                label={
                  dualStream?.isCapturingScreen
                    ? 'Capturing'
                    : dualStream?.speakerConnectionState === 'connecting'
                      ? 'Connecting...'
                      : 'Ready'
                }
                color={
                  dualStream?.isCapturingScreen
                    ? 'success'
                    : dualStream?.speakerConnectionState === 'connecting'
                      ? 'warning'
                      : 'default'
                }
                size="small"
                variant="outlined"
              />
              {!dualStream?.isCapturingScreen && (
                <Button
                  size="small"
                  startIcon={<Iconify icon="material-symbols:screen-share" width={16} />}
                  onClick={onStartScreenCapture}
                  variant="outlined"
                >
                  Start Capture
                </Button>
              )}
              {dualStream?.isCapturingScreen && (
                <Button
                  size="small"
                  startIcon={<Iconify icon="material-symbols:stop-screen-share" width={16} />}
                  onClick={onStopScreenCapture}
                  variant="outlined"
                  color="secondary"
                >
                  Stop Capture
                </Button>
              )}
            </Stack>

            {/* Screen Capture Instructions */}
            {!dualStream?.isCapturingScreen &&
              dualStream?.speakerConnectionState !== 'connected' && (
                <Box
                  sx={{
                    p: 2,
                    bgcolor: 'info.lighter',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'info.main',
                  }}
                >
                  <Typography variant="body2" color="info.main" sx={{ fontWeight: 'bold', mb: 1 }}>
                    📋 Before clicking &ldquo;Start Capture&rdquo;:
                  </Typography>
                  <Stack spacing={0.5}>
                    <Typography variant="caption" color="info.dark">
                      1. Make sure audio is playing in the tab/app you want to capture
                    </Typography>
                    <Typography variant="caption" color="info.dark">
                      2. When the browser dialog appears, look for audio options:
                    </Typography>
                    <Box sx={{ ml: 2 }}>
                      <Typography variant="caption" color="info.dark">
                        • Chrome: Check &lsquo;Share tab audio&rsquo; or &lsquo;Share system
                        audio&rsquo;
                      </Typography>
                      <Typography variant="caption" color="info.dark">
                        • Firefox: Check &lsquo;Share audio&rsquo; option
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="info.dark">
                      3. Select the tab/screen that is playing audio
                    </Typography>
                  </Stack>
                </Box>
              )}

            <Stack direction="row" alignItems="center" spacing={2}>
              <Iconify icon="material-symbols:mic" width={20} />
              <Typography variant="body2">Microphone Audio</Typography>
              <Chip
                label={
                  dualStream?.microphoneConnected
                    ? 'Connected'
                    : dualStream?.microphoneConnectionState === 'connecting'
                      ? 'Connecting...'
                      : 'Ready'
                }
                color={
                  dualStream?.microphoneConnected
                    ? 'success'
                    : dualStream?.microphoneConnectionState === 'connecting'
                      ? 'warning'
                      : 'default'
                }
                size="small"
                variant="outlined"
              />
            </Stack>

            {dualStream?.speakerError && (
              <Box
                sx={{
                  p: 2,
                  bgcolor: 'error.lighter',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'error.main',
                }}
              >
                <Typography variant="body2" color="error.main" sx={{ fontWeight: 'bold', mb: 1 }}>
                  ❌ Speaker Audio Error
                </Typography>
                <Typography
                  variant="caption"
                  color="error.dark"
                  sx={{ whiteSpace: 'pre-line', lineHeight: 1.4 }}
                >
                  {dualStream.speakerError}
                </Typography>

                {/* Show specific help if error contains screen sharing success indicator */}
                {dualStream.speakerError.includes('successfully shared your screen') && (
                  <Box sx={{ mt: 2, p: 1.5, bgcolor: 'warning.lighter', borderRadius: 1 }}>
                    <Typography
                      variant="body2"
                      color="warning.main"
                      sx={{ fontWeight: 'bold', mb: 1 }}
                    >
                      🎯 Almost there! You shared your screen but missed the audio option.
                    </Typography>
                    <Typography variant="caption" color="warning.dark">
                      When you click &ldquo;Try Screen Capture Again&rdquo;, pay close attention to
                      the audio checkbox in the browser dialog - it&rsquo;s easy to miss!
                    </Typography>
                  </Box>
                )}
              </Box>
            )}

            {dualStream?.microphoneError && (
              <Typography variant="caption" color="error">
                Microphone Error: {dualStream.microphoneError}
              </Typography>
            )}

            {/* Retry button for speaker errors */}
            {dualStream?.speakerError && dualStream?.speakerConnectionState === 'error' && (
              <Box sx={{ mt: 1 }}>
                <Button
                  size="small"
                  startIcon={<Iconify icon="material-symbols:refresh" width={16} />}
                  onClick={onStartScreenCapture}
                  variant="outlined"
                  color="primary"
                >
                  Try Screen Capture Again
                </Button>
              </Box>
            )}
          </Stack>
        )}
      </Stack>
    </Stack>
  </Box>
);

export default DualTranscriptionControls;
