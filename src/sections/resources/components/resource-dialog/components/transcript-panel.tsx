import type { Transcription } from 'src/types';

import { useMemo } from 'react';

import { Box, Chip, Stack, Divider, Typography } from '@mui/material';

import { fSecsToTime } from 'src/utils/format-time';

import { TranscriptStatus } from 'src/types';

import { Scrollbar } from 'src/components/scrollbar';

const TranscriptPanel: React.FC<{
  status?: TranscriptStatus;
  transcriptions?: Transcription[];
}> = ({ status, transcriptions = [] }) => {
  const transcriptionDisplay = useMemo(() => {
    if (status === TranscriptStatus.InProgress) {
      return <Typography variant="h6">Transcription is being generated...</Typography>;
    }
    if (status === TranscriptStatus.Failed) {
      return <Typography variant="h6">Failed to generate transcription</Typography>;
    }

    if (!transcriptions.length) return null;

    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {(transcriptions ?? []).map((item) => (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, pr: 2 }}>
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Typography variant="subtitle2">{item.nameFromRevAi}:</Typography>
              <Chip
                variant="outlined"
                color="default"
                size="small"
                label={fSecsToTime(item.startTime)}
              />
            </Stack>
            <Typography variant="body2">{item.content}</Typography>
          </Box>
        ))}
      </Box>
    );
  }, [transcriptions, status]);

  return (
    <>
      <Typography variant="subtitle1" color="textPrimary" sx={{ fontSize: 16 }}>
        Transcript
      </Typography>
      <Divider sx={{ my: 2 }} />
      <Scrollbar sx={{ height: '100%', mt: 1 }}>{transcriptionDisplay}</Scrollbar>
    </>
  );
};

export default TranscriptPanel;
