import type { SxProps } from '@mui/material';

import { useRef, useMemo, useEffect } from 'react';

import { Box, Fade, Paper, Stack, Button, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface LiveTranscriptionSegment {
  id?: string;
  text: string;
  timestamp?: Date;
  confidence?: number;
  speakerId?: string;
  isFinal?: boolean;
}

interface Props {
  segments: LiveTranscriptionSegment[];
  isRecording: boolean;
  onClear: () => void;
  sx?: SxProps;
}

const SingleTranscriptionChat: React.FC<Props> = ({ segments, isRecording, onClear, sx = {} }) => {
  const chatScrollRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new segments arrive
  useEffect(() => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  }, [segments]);

  const groupSegmentsByPunctuation = (segmentList: LiveTranscriptionSegment[]) => {
    const groups: LiveTranscriptionSegment[][] = [];
    let currentGroup: LiveTranscriptionSegment[] = [];

    segmentList.forEach((segment) => {
      currentGroup.push(segment);

      // Check if this segment ends a sentence
      const text = segment.text.trim();
      if (text.endsWith('.') || text.endsWith('?') || text.endsWith('!')) {
        groups.push([...currentGroup]);
        currentGroup = [];
      }
    });

    // Add remaining segments as partial group if any
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }

    return groups;
  };

  const renderChatBubble = (segmentGroup: LiveTranscriptionSegment[], isPartial: boolean) => {
    if (!segmentGroup.length) return null;

    // Combine all segment texts in the group
    const combinedText = segmentGroup
      .map((s) => s.text)
      .join('')
      .trim();
    const latestTimestamp = segmentGroup[segmentGroup.length - 1]?.timestamp;
    const averageConfidence =
      segmentGroup.reduce((sum, s) => sum + (s.confidence || 0), 0) / segmentGroup.length;
    const speakerId = segmentGroup[0]?.speakerId;

    // Generate a unique key for the group
    const groupKey =
      segmentGroup[0]?.id ||
      `group-${segmentGroup
        .map((s) => s.text)
        .join('-')
        .slice(0, 20)}`;

    return (
      <Fade in key={groupKey} timeout={300}>
        <Box sx={{ mb: 1, display: 'flex', justifyContent: 'flex-start' }}>
          <Paper
            elevation={isPartial ? 0 : 1}
            sx={{
              p: 2,
              maxWidth: '95%',
              bgcolor: isPartial ? 'grey.100' : 'primary.light',
              borderRadius: 3,
              position: 'relative',
              border: isPartial ? '1px dashed' : 'none',
              borderColor: isPartial ? 'grey.300' : 'transparent',
              transition: 'all 0.3s ease',
            }}
          >
            <Typography
              variant="body2"
              color={isPartial ? 'text.secondary' : 'primary.contrastText'}
              sx={{
                wordBreak: 'break-word',
                lineHeight: 1.4,
              }}
            >
              {combinedText}
              {isPartial && (
                <Box
                  component="span"
                  sx={{
                    ml: 1,
                    opacity: 0.6,
                    fontWeight: 'bold',
                    animation: 'blink 1.5s infinite',
                    '@keyframes blink': {
                      '0%, 50%': { opacity: 0.6 },
                      '51%, 100%': { opacity: 0.2 },
                    },
                  }}
                >
                  ●●●
                </Box>
              )}
            </Typography>

            <Stack direction="row" spacing={1} alignItems="center" sx={{ mt: 0.5 }}>
              <Typography
                variant="caption"
                sx={{
                  fontSize: '0.7rem',
                  opacity: 0.7,
                  color: isPartial ? 'text.disabled' : 'primary.contrastText',
                }}
              >
                {latestTimestamp ? new Date(latestTimestamp).toLocaleTimeString() : 'Now'}
              </Typography>
              {averageConfidence > 0 && (
                <>
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.7rem',
                      opacity: 0.7,
                      color: isPartial ? 'text.disabled' : 'primary.contrastText',
                    }}
                  >
                    •
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.7rem',
                      opacity: 0.7,
                      color: isPartial ? 'text.disabled' : 'primary.contrastText',
                    }}
                  >
                    {Math.round(averageConfidence * 100)}%
                  </Typography>
                </>
              )}
              {speakerId && (
                <>
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.7rem',
                      opacity: 0.7,
                      color: isPartial ? 'text.disabled' : 'primary.contrastText',
                    }}
                  >
                    •
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '0.7rem',
                      opacity: 0.7,
                      color: isPartial ? 'text.disabled' : 'primary.contrastText',
                    }}
                  >
                    {speakerId}
                  </Typography>
                </>
              )}
            </Stack>
          </Paper>
        </Box>
      </Fade>
    );
  };

  const renderEmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
        opacity: 0.6,
      }}
    >
      <Iconify
        icon="material-symbols:chat-bubble-outline"
        width={48}
        color="text.disabled"
        sx={{ mb: 2 }}
      />
      <Typography variant="body2" color="text.secondary" textAlign="center">
        No transcription yet
      </Typography>
      <Typography variant="caption" color="text.disabled" textAlign="center">
        Start recording to see live transcription
      </Typography>
    </Box>
  );

  // Group segments for chat display
  const groupedSegments = useMemo(() => {
    if (!segments.length) return [];
    return groupSegmentsByPunctuation(segments);
  }, [segments]);

  return (
    <Paper
      elevation={2}
      sx={{
        mt: 3,
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        ...sx,
      }}
    >
      {/* Chat Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify icon="material-symbols:chat-bubble" width={18} />
            <Typography variant="subtitle2" sx={{ fontSize: 14 }}>
              Live Transcription
            </Typography>
            {isRecording && (
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: 'success.main',
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 },
                  },
                }}
              />
            )}
          </Stack>
          {segments.length > 0 && (
            <Button
              size="small"
              startIcon={<Iconify icon="material-symbols:clear-all" width={16} />}
              onClick={onClear}
              sx={{ minWidth: 'auto', px: 1, fontSize: '0.75rem' }}
            >
              Clear
            </Button>
          )}
        </Stack>
      </Box>

      {/* Chat Content */}
      <Box
        ref={chatScrollRef}
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 2,
          '&::-webkit-scrollbar': {
            width: 6,
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'grey.100',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'grey.400',
            borderRadius: 3,
          },
        }}
      >
        {groupedSegments.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            {renderEmptyState()}
          </Box>
        ) : (
          <Box>
            {groupedSegments.map((segmentGroup, index) => {
              // Check if this is the last group (partial sentence)
              const isPartial =
                index === groupedSegments.length - 1 &&
                !segmentGroup.some((segment) => {
                  const text = segment.text.trim();
                  return text.endsWith('.') || text.endsWith('?') || text.endsWith('!');
                });

              return renderChatBubble(segmentGroup, isPartial);
            })}
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default SingleTranscriptionChat;
