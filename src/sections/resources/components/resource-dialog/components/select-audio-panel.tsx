/* eslint-disable perfectionist/sort-named-imports */
import type { SxProps } from '@mui/material';

import { useEffect, useCallback } from 'react';

import { Box, IconButton, Paper, Stack, Typography } from '@mui/material';

import { useLiveTranscription } from 'src/hooks/use-live-transcription';

import { Iconify } from 'src/components/iconify';

import AudioDeviceSelector from './audio-device-selector';
import DualTranscriptionChat from './dual-transcription-chat';
import SingleTranscriptionChat from './single-transcription-chat';
import DualTranscriptionControls from './dual-transcription-controls';

interface Props {
  onClose?: () => void;
  sx?: SxProps;
}

const SelectAudioPanel: React.FC<Props> = ({ onClose, sx = {} }) => {
  const {
    bootstrap,
    audioDevices,
    selectedAudioDeviceId,
    switchAudioDevice,
    state,
    startTranscription,
    stopTranscription,
    clearTranscription,
    startDualTranscription,
    stopDualTranscription,
    startScreenCapture,
    stopScreenCapture,
  } = useLiveTranscription();

  useEffect(() => {
    bootstrap();
  }, [bootstrap]);

  useEffect(() => {
    console.log('Transcription segments updated:', state.segments.map((x) => x.text).join(' '));
  }, [state.segments]);

  const handleDeviceChange = useCallback(
    (deviceId: string) => {
      switchAudioDevice(deviceId).catch(console.error);
    },
    [switchAudioDevice]
  );

  const handleStartRecording = () => {
    startTranscription({
      language: 'en',
    });
  };

  const handleStopRecording = () => {
    if (state.isRecording) {
      stopTranscription();
    }
  };

  const handleClearTranscription = () => {
    clearTranscription();
  };

  const handleStartDualTranscription = () => {
    startDualTranscription({
      language: 'en',
      isDualMode: true,
    });
  };

  const handleStopDualTranscription = () => {
    if (state.isDualMode) {
      stopDualTranscription();
    }
  };

  const handleToggleDualMode = (enabled: boolean) => {
    if (enabled) {
      handleStartDualTranscription();
    } else {
      handleStopDualTranscription();
    }
  };

  const handleStartScreenCapture = () => {
    startScreenCapture();
  };

  const handleStopScreenCapture = () => {
    stopScreenCapture();
  };

  return (
    <Paper
      elevation={1}
      sx={{
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '25%',
        },
        height: ['50%', '100%'],
        ...sx,
      }}
    >
      <Box>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="subtitle1" color="textPrimary" sx={{ fontSize: 14 }}>
            Audio Setup
          </Typography>
          <IconButton onClick={onClose} sx={{ p: 0 }}>
            <Iconify icon="eva:close-fill" width={20} />
          </IconButton>
        </Stack>
      </Box>

      {/* Audio Device Selection */}
      <AudioDeviceSelector
        audioDevices={audioDevices}
        selectedAudioDeviceId={selectedAudioDeviceId || ''}
        onDeviceChange={handleDeviceChange}
        connectionState={state.connectionState}
        isRecording={state.isRecording}
        isConnected={state.isConnected}
        error={state.error}
        onStartRecording={handleStartRecording}
        onStopRecording={handleStopRecording}
        sx={{ mt: 3 }}
      />

      {/* Dual Transcription Controls */}
      <DualTranscriptionControls
        isDualMode={state.isDualMode || false}
        isRecording={state.isRecording}
        dualStream={state.dualStream}
        onToggleDualMode={handleToggleDualMode}
        onStartScreenCapture={handleStartScreenCapture}
        onStopScreenCapture={handleStopScreenCapture}
        sx={{ mt: 3 }}
      />

      {/* Conditional Chat Display */}
      {state.isDualMode ? (
        <DualTranscriptionChat
          segments={state.segments}
          isRecording={state.isRecording}
          isDualMode={state.isDualMode}
          speakerConnected={state.dualStream?.speakerConnected || false}
          microphoneConnected={state.dualStream?.microphoneConnected || false}
          onClear={handleClearTranscription}
        />
      ) : (
        <SingleTranscriptionChat
          segments={state.segments}
          isRecording={state.isRecording}
          onClear={handleClearTranscription}
        />
      )}
    </Paper>
  );
};

export default SelectAudioPanel;
