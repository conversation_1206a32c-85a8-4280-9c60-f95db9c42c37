import type { Resource } from 'src/types';

import { Box } from '@mui/material';

import { fileFormat } from 'src/components/file-thumbnail';

import ResourceThumbnail from '../../resource-card/components/resource-thumbnail';
import ResourcePreviewer from '../../resource-card/components/resource-previewer';

const SummaryPanel: React.FC<{
  resource: Resource;
}> = ({ resource }) => {
  const fFormat = fileFormat(resource.fileName);
  const isAudioOrVideo = fFormat === 'audio' || fFormat === 'video';

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      {isAudioOrVideo ? (
        <ResourcePreviewer data={resource} />
      ) : (
        <ResourceThumbnail data={resource} />
      )}
    </Box>
  );
};

export default SummaryPanel;
