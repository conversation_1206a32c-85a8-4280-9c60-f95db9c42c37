import type { AudioSource, LiveTranscriptionSegment } from 'src/types';

import React, { useRef, useMemo, useEffect } from 'react';

import { Box, Fade, Chip, Paper, Stack, Button, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface Props {
  segments: LiveTranscriptionSegment[];
  isRecording: boolean;
  isDualMode: boolean;
  speakerConnected: boolean;
  microphoneConnected: boolean;
  onClear?: () => void;
}

interface SegmentGroup {
  segments: LiveTranscriptionSegment[];
  isPartial: boolean;
  audioSource: AudioSource;
  timestamp: Date;
}

const DualTranscriptionChat: React.FC<Props> = ({
  segments,
  isRecording,
  isDualMode,
  speakerConnected,
  microphoneConnected,
  onClear,
}) => {
  const chatScrollRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new segments arrive
  useEffect(() => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  }, [segments]);

  const groupSegmentsByPunctuation = (
    allSegments: LiveTranscriptionSegment[],
    source: AudioSource
  ): SegmentGroup[] => {
    const sourceSegments = allSegments.filter((segment) => segment.audioSource === source);
    const groups: SegmentGroup[] = [];
    let currentGroup: LiveTranscriptionSegment[] = [];

    sourceSegments.forEach((segment) => {
      currentGroup.push(segment);

      // Check if this segment ends a sentence
      const text = segment.text.trim();
      if (text.endsWith('.') || text.endsWith('?') || text.endsWith('!')) {
        groups.push({
          segments: [...currentGroup],
          isPartial: false,
          audioSource: source,
          timestamp: currentGroup[currentGroup.length - 1]?.timestamp || new Date(),
        });
        currentGroup = [];
      }
    });

    // Add remaining segments as partial group if any
    if (currentGroup.length > 0) {
      groups.push({
        segments: [...currentGroup],
        isPartial: true,
        audioSource: source,
        timestamp: currentGroup[currentGroup.length - 1]?.timestamp || new Date(),
      });
    }

    return groups;
  };

  // Combine and sort all groups by timestamp for conversation flow
  const allGroups = useMemo(() => {
    const speakerGroups = groupSegmentsByPunctuation(segments, 'speaker');
    const microphoneGroups = groupSegmentsByPunctuation(segments, 'microphone');

    return [...speakerGroups, ...microphoneGroups].sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );
  }, [segments]);

  const renderChatBubble = (group: SegmentGroup) => {
    if (!group.segments.length) return null;

    const isSpeaker = group.audioSource === 'speaker';
    const alignRight = !isSpeaker; // Microphone on right, speaker on left

    // Combine all segment texts in the group
    const combinedText = group.segments
      .map((s) => s.text)
      .join('')
      .trim();
    const latestTimestamp = group.segments[group.segments.length - 1]?.timestamp;
    const averageConfidence =
      group.segments.reduce((sum, s) => sum + (s.confidence || 0), 0) / group.segments.length;
    const speakerId = group.segments[0]?.speakerId;

    // Generate a unique key for the group
    const groupKey =
      group.segments[0]?.id ||
      `group-${group.audioSource}-${group.segments
        .map((s) => s.text)
        .join('-')
        .slice(0, 20)}`;

    const bubbleColor = isSpeaker ? 'info.light' : 'primary.light';
    const textColor = isSpeaker ? 'info.contrastText' : 'primary.contrastText';

    return (
      <Fade in key={groupKey} timeout={300}>
        <Box
          sx={{
            mb: 2,
            display: 'flex',
            justifyContent: alignRight ? 'flex-end' : 'flex-start',
          }}
        >
          <Box sx={{ maxWidth: '75%', display: 'flex', flexDirection: 'column' }}>
            {/* Source label */}
            <Typography
              variant="caption"
              sx={{
                fontSize: '0.7rem',
                color: 'text.secondary',
                mb: 0.5,
                alignSelf: alignRight ? 'flex-end' : 'flex-start',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              <Iconify
                icon={isSpeaker ? 'material-symbols:volume-up' : 'material-symbols:mic'}
                width={12}
              />
              {isSpeaker ? 'Speaker' : 'Microphone'}
            </Typography>

            <Paper
              elevation={group.isPartial ? 0 : 1}
              sx={{
                p: 2,
                bgcolor: group.isPartial ? 'grey.100' : bubbleColor,
                borderRadius: 3,
                position: 'relative',
                border: group.isPartial ? '1px dashed' : 'none',
                borderColor: group.isPartial ? 'grey.300' : 'transparent',
                transition: 'all 0.3s ease',
                // Chat bubble tail
                '&::before': group.isPartial
                  ? {}
                  : {
                      content: '""',
                      position: 'absolute',
                      top: 8,
                      [alignRight ? 'right' : 'left']: -6,
                      width: 0,
                      height: 0,
                      borderStyle: 'solid',
                      borderWidth: alignRight ? '6px 0 6px 6px' : '6px 6px 6px 0',
                      borderColor: alignRight
                        ? `transparent transparent transparent ${bubbleColor}`
                        : `transparent ${bubbleColor} transparent transparent`,
                    },
              }}
            >
              <Typography
                variant="body2"
                color={group.isPartial ? 'text.secondary' : textColor}
                sx={{
                  wordBreak: 'break-word',
                  lineHeight: 1.4,
                }}
              >
                {combinedText}
                {group.isPartial && (
                  <Box
                    component="span"
                    sx={{
                      ml: 1,
                      opacity: 0.6,
                      fontWeight: 'bold',
                      animation: 'blink 1.5s infinite',
                      '@keyframes blink': {
                        '0%, 50%': { opacity: 0.6 },
                        '51%, 100%': { opacity: 0.2 },
                      },
                    }}
                  >
                    ●●●
                  </Box>
                )}
              </Typography>

              <Stack direction="row" spacing={1} alignItems="center" sx={{ mt: 0.5 }}>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: '0.7rem',
                    opacity: 0.7,
                    color: group.isPartial ? 'text.disabled' : textColor,
                  }}
                >
                  {latestTimestamp ? new Date(latestTimestamp).toLocaleTimeString() : 'Now'}
                </Typography>
                {averageConfidence > 0 && (
                  <>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.7rem',
                        opacity: 0.7,
                        color: group.isPartial ? 'text.disabled' : textColor,
                      }}
                    >
                      •
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.7rem',
                        opacity: 0.7,
                        color: group.isPartial ? 'text.disabled' : textColor,
                      }}
                    >
                      {Math.round(averageConfidence * 100)}%
                    </Typography>
                  </>
                )}
                {speakerId && (
                  <>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.7rem',
                        opacity: 0.7,
                        color: group.isPartial ? 'text.disabled' : textColor,
                      }}
                    >
                      •
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.7rem',
                        opacity: 0.7,
                        color: group.isPartial ? 'text.disabled' : textColor,
                      }}
                    >
                      {speakerId}
                    </Typography>
                  </>
                )}
              </Stack>
            </Paper>
          </Box>
        </Box>
      </Fade>
    );
  };

  const renderEmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 6,
        opacity: 0.6,
        height: '100%',
      }}
    >
      <Stack direction="row" spacing={3} alignItems="center" sx={{ mb: 3 }}>
        <Box sx={{ textAlign: 'center' }}>
          <Iconify
            icon="material-symbols:volume-up"
            width={48}
            color={speakerConnected ? 'info.main' : 'text.disabled'}
            sx={{ mb: 1 }}
          />
          <Typography variant="caption" color="text.secondary">
            Speaker
          </Typography>
          <br />
          <Chip
            label={speakerConnected ? 'Connected' : 'Disconnected'}
            color={speakerConnected ? 'success' : 'default'}
            size="small"
            variant="outlined"
          />
        </Box>

        <Typography variant="h6" color="text.disabled">
          vs
        </Typography>

        <Box sx={{ textAlign: 'center' }}>
          <Iconify
            icon="material-symbols:mic"
            width={48}
            color={microphoneConnected ? 'primary.main' : 'text.disabled'}
            sx={{ mb: 1 }}
          />
          <Typography variant="caption" color="text.secondary">
            Microphone
          </Typography>
          <br />
          <Chip
            label={microphoneConnected ? 'Connected' : 'Disconnected'}
            color={microphoneConnected ? 'success' : 'default'}
            size="small"
            variant="outlined"
          />
        </Box>
      </Stack>

      <Typography variant="body2" color="text.secondary" textAlign="center">
        Start speaking or playing audio to see the conversation
      </Typography>
      <Typography variant="caption" color="text.disabled" textAlign="center" sx={{ mt: 1 }}>
        Speaker messages appear on the left • Microphone messages appear on the right
      </Typography>
    </Box>
  );

  if (!isDualMode) {
    return null;
  }

  return (
    <Paper
      elevation={2}
      sx={{
        mt: 3,
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}
    >
      {/* Chat Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify icon="material-symbols:chat-bubble" width={18} />
            <Typography variant="subtitle2" sx={{ fontSize: 14 }}>
              Dual Transcription
            </Typography>
            {isRecording && (speakerConnected || microphoneConnected) && (
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: 'success.main',
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 },
                  },
                }}
              />
            )}
          </Stack>
          {segments.length > 0 && onClear && (
            <Button
              size="small"
              startIcon={<Iconify icon="material-symbols:clear-all" width={16} />}
              onClick={onClear}
              sx={{ minWidth: 'auto', px: 1, fontSize: '0.75rem' }}
            >
              Clear
            </Button>
          )}
        </Stack>

        {/* Connection Status Bar */}
        <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 1 }}>
          <Stack direction="row" alignItems="center" spacing={0.5}>
            <Iconify
              icon="material-symbols:volume-up"
              width={14}
              color={speakerConnected ? 'success.main' : 'text.disabled'}
            />
            <Typography
              variant="caption"
              color={speakerConnected ? 'success.main' : 'text.disabled'}
            >
              Speaker {speakerConnected ? 'Connected' : 'Disconnected'}
            </Typography>
          </Stack>

          <Box sx={{ width: 4, height: 4, borderRadius: '50%', bgcolor: 'grey.300' }} />

          <Stack direction="row" alignItems="center" spacing={0.5}>
            <Iconify
              icon="material-symbols:mic"
              width={14}
              color={microphoneConnected ? 'success.main' : 'text.disabled'}
            />
            <Typography
              variant="caption"
              color={microphoneConnected ? 'success.main' : 'text.disabled'}
            >
              Microphone {microphoneConnected ? 'Connected' : 'Disconnected'}
            </Typography>
          </Stack>
        </Stack>
      </Box>

      {/* Chat Content */}
      <Box
        ref={chatScrollRef}
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 2,
          '&::-webkit-scrollbar': {
            width: 6,
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'grey.100',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'grey.400',
            borderRadius: 3,
          },
        }}
      >
        {allGroups.length === 0 ? (
          renderEmptyState()
        ) : (
          <Box>{allGroups.map((group) => renderChatBubble(group))}</Box>
        )}
      </Box>
    </Paper>
  );
};

export default DualTranscriptionChat;
