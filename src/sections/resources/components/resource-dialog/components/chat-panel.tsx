import type { Resource } from 'src/types';

import { useMemo, useEffect } from 'react';

import { Divider, useTheme, Typography, useMediaQuery } from '@mui/material';

import useAiAgent from 'src/hooks/ai-agent';

import { TranscriptStatus } from 'src/types';

import ChatBox from 'src/sections/chat/chat-box';

interface Props {
  resource: Resource;
}

const ChatPanel: React.FC<Props> = ({ resource }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const {
    messages,
    isReplying,
    allowSendMessage,
    sendMessage,
    isStartingConversation,
    startConversationForResource,
  } = useAiAgent();

  const isTranscriptionReady = useMemo(
    () => resource?.transcriptionJobStatus === TranscriptStatus.Completed,
    [resource?.transcriptionJobStatus]
  );

  useEffect(() => {
    if (!isTranscriptionReady || !resource) return;

    startConversationForResource(resource);
  }, [isTranscriptionReady, resource]);

  return (
    <>
      <Typography variant="subtitle1" color="textPrimary" sx={{ fontSize: 16 }}>
        Chat with Aida
      </Typography>
      <Divider sx={{ my: 2 }} />
      <ChatBox
        containerSx={{
          display: 'flex',
          flexDirection: 'column',
          height: 'auto',
        }}
        messageListSx={{ height: isMobile ? '60vh' : '75vh', pt: 2, px: 0, pr: 1 }}
        messages={messages}
        loading={isStartingConversation}
        isReplying={isReplying}
        disabled={!allowSendMessage}
        sendMessage={sendMessage}
      />
    </>
  );
};

export default ChatPanel;
