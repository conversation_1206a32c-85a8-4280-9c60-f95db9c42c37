import type { Resource } from 'src/types';

import { Divider, Typography } from '@mui/material';

import ResourcePreviewer from '../../resource-card/components/resource-previewer';

const PreviewPanel: React.FC<{
  resource: Resource;
}> = ({ resource }) => (
  <>
    <Typography variant="subtitle1" color="textPrimary" sx={{ fontSize: 16 }}>
      Preview
    </Typography>
    <Divider sx={{ my: 2 }} />
    <ResourcePreviewer data={resource} />
  </>
);

export default PreviewPanel;
