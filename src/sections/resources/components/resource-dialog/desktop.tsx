import type { Resource } from 'src/types';
import type { Theme, SxProps } from '@mui/material/styles';
import type { TransitionProps } from '@mui/material/transitions';

import React, { useMemo, useState, forwardRef } from 'react';

import Dialog from '@mui/material/Dialog';
import { Box, Slide, Stack } from '@mui/material';

import { TranscriptStatus } from 'src/types';

import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import { LoadingScreen } from 'src/components/loading-screen';

import { MainPanel } from '.';
import { Panel } from './components/layout';
import ChatPanel from './components/chat-panel';
import InfoPanel from './components/info-panel';
import NotesPanel from './components/notes-panel';
import PreviewPanel from './components/preview-panel';
import TranscriptPanel from './components/transcript-panel';

// Common styles for content panels
const contentPanelStyles: SxProps<Theme> = {
  minWidth: '25%',
  flex: 1,
  overflow: 'auto',
};

const Transition = forwardRef(
  (props: TransitionProps & { children: React.ReactElement }, ref: React.Ref<unknown>) => (
    <Slide direction="up" ref={ref} {...props} />
  )
);

const ResourceDialogDesktop: React.FC<{
  loading: boolean;
  resource?: Resource;
  onClose: () => void;
}> = ({ loading, resource, onClose }) => {
  const fFormat = fileFormat(resource?.fileName ?? '');
  const isAudioOrVideo = fFormat === 'audio' || fFormat === 'video';

  const [activePanels, setActivePanels] = useState<Record<MainPanel, boolean>>({
    [MainPanel.Transcript]: false,
    [MainPanel.Preview]: false,
    [MainPanel.Notes]: false,
  });

  // Determine which panels are available based on file type
  const navigationItems = useMemo(
    () => [
      ...(isAudioOrVideo
        ? [
            {
              label: 'Transcript',
              value: MainPanel.Transcript,
              icon: <Iconify icon="material-symbols:speech-to-text-rounded" />,
            },
          ]
        : [
            {
              label: 'Preview',
              value: MainPanel.Preview,
              icon: <Iconify icon="material-symbols:preview" />,
            },
          ]),
      {
        label: 'Notes',
        value: MainPanel.Notes,
        icon: <Iconify icon="material-symbols:notes-rounded" />,
      },
    ],
    [isAudioOrVideo]
  );

  // Check if any content panels are active
  const hasActiveContentPanels = Object.values(activePanels).some(Boolean);

  // Toggle a panel on/off
  const handleTogglePanel = (panel: MainPanel) => {
    setActivePanels((prev) => {
      const newState = { ...prev };

      // Toggle the selected panel
      newState[panel] = !prev[panel];

      // If this is a Transcript or Preview panel, ensure only one can be active
      if ((panel === MainPanel.Transcript || panel === MainPanel.Preview) && newState[panel]) {
        // If turning on this panel, turn off the other one
        newState[MainPanel.Transcript] = panel === MainPanel.Transcript;
        newState[MainPanel.Preview] = panel === MainPanel.Preview;
      }

      return newState;
    });
  };

  // Render content panel based on type
  const renderContentPanel = (panelType: MainPanel) => {
    if (!activePanels[panelType]) return null;

    let content;
    switch (panelType) {
      case MainPanel.Notes:
        content = <NotesPanel resourceId={resource!.id} />;
        break;
      case MainPanel.Transcript:
        content = (
          <TranscriptPanel
            status={resource!.transcriptionJobStatus ?? TranscriptStatus.InProgress}
            transcriptions={resource!.transcription ?? []}
          />
        );
        break;
      case MainPanel.Preview:
        content = <PreviewPanel resource={resource!} />;
        break;
      default:
        return null;
    }

    return <Panel sx={contentPanelStyles}>{content}</Panel>;
  };

  return (
    <Dialog
      fullScreen
      open
      onClose={onClose}
      TransitionComponent={Transition}
      PaperProps={{
        sx: {
          background: 'transparent',
          height: '100vh',
          padding: 3,
          overflow: 'hidden',
        },
      }}
    >
      {loading || !resource ? (
        <LoadingScreen />
      ) : (
        <Stack direction="row" gap={2} height="100%" key={resource.id}>
          {/* Info Panel - expands to 50% when no content panels are active */}
          <Panel sx={{ width: hasActiveContentPanels ? '25%' : '50%' }}>
            <InfoPanel
              resource={resource}
              onClose={onClose}
              activePanels={activePanels}
              onTogglePanel={handleTogglePanel}
              navigationItems={navigationItems}
            />
          </Panel>

          {/* Content Panels Container - only shown when at least one panel is active */}
          {hasActiveContentPanels && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: 2,
                width: '50%',
                overflow: 'hidden',
              }}
            >
              {renderContentPanel(MainPanel.Transcript)}
              {renderContentPanel(MainPanel.Preview)}
              {renderContentPanel(MainPanel.Notes)}
            </Box>
          )}

          {/* Chat Panel - expands to 50% when no content panels are active */}
          <Panel sx={{ width: hasActiveContentPanels ? '25%' : '50%' }}>
            <ChatPanel resource={resource} />
          </Panel>
        </Stack>
      )}
    </Dialog>
  );
};

export default ResourceDialogDesktop;
