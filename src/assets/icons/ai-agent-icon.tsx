import type { SvgIconProps } from '@mui/material/SvgIcon';

import { memo, forwardRef } from 'react';

import { useTheme } from '@mui/material';
import SvgIcon from '@mui/material/SvgIcon';

// ----------------------------------------------------------------------

const AiAgentIcon = forwardRef<SVGSVGElement, SvgIconProps>((props, ref) => {
  const theme = useTheme();
  const { sx, ...other } = props;

  const lightIcon = (
    <>
      <mask
        id="mask0_3815_833"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="56"
        height="56"
      >
        <rect
          x="56"
          width="56"
          height="56"
          rx="28"
          transform="rotate(90 56 0)"
          fill="#D9D9D9"
          fillOpacity="0.48"
        />
      </mask>
      <g mask="url(#mask0_3815_833)">
        <rect
          x="55.5"
          y="0.5"
          width="55"
          height="55"
          rx="27.5"
          transform="rotate(90 55.5 0.5)"
          fill="white"
          fillOpacity="0.96"
        />
        <rect
          x="55.5"
          y="0.5"
          width="55"
          height="55"
          rx="27.5"
          transform="rotate(90 55.5 0.5)"
          fill="#2656C9"
          fillOpacity="0.16"
        />
        <rect
          x="67.5"
          y="8.5"
          width="55"
          height="55"
          rx="27.5"
          transform="rotate(90 67.5 8.5)"
          stroke="url(#paint0_linear_3736_3733)"
          strokeOpacity="0.72"
        />
        <g filter="url(#filter0_f_3815_833)">
          <rect
            x="77.3333"
            y="26.6666"
            width="56"
            height="56"
            rx="28"
            transform="rotate(90 77.3333 26.6666)"
            fill="#F3F0D6"
            fillOpacity="0.64"
          />
        </g>
      </g>
      <path
        d="M36.8357 25.0251C36.9169 25.2036 37.0948 25.3182 37.2909 25.3182C37.487 25.3182 37.665 25.2036 37.7461 25.0251L39.5776 20.9958L43.6069 19.1642C43.7854 19.0831 43.9 18.9051 43.9 18.7091C43.9 18.513 43.7854 18.335 43.6069 18.2539L39.5776 16.4224L37.7461 12.3931C37.665 12.2146 37.487 12.1 37.2909 12.1C37.0948 12.1 36.9169 12.2146 36.8357 12.3931L35.0042 16.4224L30.9749 18.2539C30.7964 18.335 30.6818 18.513 30.6818 18.7091C30.6818 18.9051 30.7964 19.0831 30.9749 19.1642L35.0042 20.9958L36.8357 25.0251ZM22.963 15.5749L19.2224 23.8042L10.9931 27.5448C10.8146 27.6259 10.7 27.8039 10.7 28C10.7 28.196 10.8146 28.374 10.9931 28.4552L19.2224 32.1958L22.963 40.4251C23.0441 40.6036 23.2221 40.7182 23.4182 40.7182C23.6143 40.7182 23.7922 40.6036 23.8734 40.4251L27.614 32.1958L35.8433 28.4552C36.0218 28.374 36.1364 28.196 36.1364 28C36.1364 27.8039 36.0218 27.6259 35.8433 27.5448L27.614 23.8042L23.8734 15.5749C23.7922 15.3964 23.6143 15.2818 23.4182 15.2818C23.2221 15.2818 23.0441 15.3964 22.963 15.5749ZM37.7461 30.9749C37.665 30.7964 37.487 30.6818 37.2909 30.6818C37.0948 30.6818 36.9169 30.7964 36.8357 30.9749L35.0042 35.0042L30.9749 36.8357C30.7964 36.9168 30.6818 37.0948 30.6818 37.2909C30.6818 37.487 30.7964 37.6649 30.9749 37.7461L35.0042 39.5776L36.8357 43.6069C36.9169 43.7854 37.0948 43.9 37.2909 43.9C37.487 43.9 37.665 43.7854 37.7461 43.6069L39.5776 39.5776L43.6069 37.7461C43.7854 37.6649 43.9 37.487 43.9 37.2909C43.9 37.0948 43.7854 36.9168 43.6069 36.8357L39.5776 35.0042L37.7461 30.9749Z"
        fill="white"
        stroke="#090A2C"
        strokeLinejoin="round"
      />
      <mask
        id="mask1_3815_833"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="11"
        y="12"
        width="33"
        height="32"
      >
        <path
          d="M37.2909 24.8182L39.2 20.6182L43.4 18.7091L39.2 16.8L37.2909 12.6L35.3818 16.8L31.1818 18.7091L35.3818 20.6182L37.2909 24.8182ZM27.2364 24.1818L23.4182 15.7818L19.6 24.1818L11.2 28L19.6 31.8182L23.4182 40.2182L27.2364 31.8182L35.6364 28L27.2364 24.1818ZM37.2909 31.1818L35.3818 35.3818L31.1818 37.2909L35.3818 39.2L37.2909 43.4L39.2 39.2L43.4 37.2909L39.2 35.3818L37.2909 31.1818Z"
          fill="url(#paint1_linear_3815_833)"
        />
      </mask>
      <g mask="url(#mask1_3815_833)">
        <path
          d="M37.2909 24.8182L39.2 20.6182L43.4 18.7091L39.2 16.8L37.2909 12.6L35.3818 16.8L31.1818 18.7091L35.3818 20.6182L37.2909 24.8182ZM27.2364 24.1818L23.4182 15.7818L19.6 24.1818L11.2 28L19.6 31.8182L23.4182 40.2182L27.2364 31.8182L35.6364 28L27.2364 24.1818ZM37.2909 31.1818L35.3818 35.3818L31.1818 37.2909L35.3818 39.2L37.2909 43.4L39.2 39.2L43.4 37.2909L39.2 35.3818L37.2909 31.1818Z"
          fill="#292595"
        />
        <g filter="url(#filter1_f_3815_833)">
          <circle cx="35" cy="39.2" r="14" fill="#F3F0D6" fillOpacity="0.4" />
        </g>
        <g filter="url(#filter2_f_3815_833)">
          <circle cx="23.8041" cy="15.3987" r="14" fill="#4FB5FF" />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_f_3815_833"
          x="-12.1667"
          y="-2.83337"
          width="99"
          height="99"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="10.75" result="effect1_foregroundBlur_3815_833" />
        </filter>
        <filter
          id="filter1_f_3815_833"
          x="5"
          y="9.19995"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="8" result="effect1_foregroundBlur_3815_833" />
        </filter>
        <filter
          id="filter2_f_3815_833"
          x="-6.19589"
          y="-14.6013"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="8" result="effect1_foregroundBlur_3815_833" />
        </filter>
        <linearGradient
          id="paint0_linear_3815_833"
          x1="112"
          y1="28.5833"
          x2="56.7778"
          y2="28.5833"
          gradientUnits="userSpaceOnUse"
        >
          <stop />
          <stop offset="1" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_3815_833"
          x1="28"
          y1="11.2"
          x2="28"
          y2="44.8"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D6A65D" />
          <stop offset="1" stopColor="#9A5607" />
        </linearGradient>
      </defs>
    </>
  );

  const darkIcon = (
    <>
      <mask
        id="mask0_3815_833"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="56"
        height="56"
      >
        <rect
          x="56"
          width="56"
          height="56"
          rx="28"
          transform="rotate(90 56 0)"
          fill="#D9D9D9"
          fillOpacity="0.48"
        />
      </mask>
      <g mask="url(#mask0_3815_833)">
        <rect
          x="55.5"
          y="0.5"
          width="55"
          height="55"
          rx="27.5"
          transform="rotate(90 55.5 0.5)"
          fill="#1C1C24"
          fillOpacity="0.96"
        />
        <rect
          x="55.5"
          y="0.5"
          width="55"
          height="55"
          rx="27.5"
          transform="rotate(90 55.5 0.5)"
          fill="#87B0FF"
          fillOpacity="0.32"
        />
        <rect
          x="55.5"
          y="0.5"
          width="55"
          height="55"
          rx="27.5"
          transform="rotate(90 55.5 0.5)"
          fill="#2656C9"
          fillOpacity="0.16"
        />
        <rect
          x="55.5"
          y="0.5"
          width="55"
          height="55"
          rx="27.5"
          transform="rotate(90 55.5 0.5)"
          stroke="url(#paint0_linear_3815_833)"
        />
        <g filter="url(#filter0_f_3815_833)">
          <rect
            x="65.3333"
            y="18.6666"
            width="56"
            height="56"
            rx="28"
            transform="rotate(90 65.3333 18.6666)"
            fill="#F3F0D6"
            fillOpacity="0.32"
          />
        </g>
      </g>
      <path
        d="M36.8357 25.0251C36.9169 25.2036 37.0948 25.3182 37.2909 25.3182C37.487 25.3182 37.665 25.2036 37.7461 25.0251L39.5776 20.9958L43.6069 19.1642C43.7854 19.0831 43.9 18.9051 43.9 18.7091C43.9 18.513 43.7854 18.335 43.6069 18.2539L39.5776 16.4224L37.7461 12.3931C37.665 12.2146 37.487 12.1 37.2909 12.1C37.0948 12.1 36.9169 12.2146 36.8357 12.3931L35.0042 16.4224L30.9749 18.2539C30.7964 18.335 30.6818 18.513 30.6818 18.7091C30.6818 18.9051 30.7964 19.0831 30.9749 19.1642L35.0042 20.9958L36.8357 25.0251ZM22.963 15.5749L19.2224 23.8042L10.9931 27.5448C10.8146 27.6259 10.7 27.8039 10.7 28C10.7 28.196 10.8146 28.374 10.9931 28.4552L19.2224 32.1958L22.963 40.4251C23.0441 40.6036 23.2221 40.7182 23.4182 40.7182C23.6143 40.7182 23.7922 40.6036 23.8734 40.4251L27.614 32.1958L35.8433 28.4552C36.0218 28.374 36.1364 28.196 36.1364 28C36.1364 27.8039 36.0218 27.6259 35.8433 27.5448L27.614 23.8042L23.8734 15.5749C23.7922 15.3964 23.6143 15.2818 23.4182 15.2818C23.2221 15.2818 23.0441 15.3964 22.963 15.5749ZM37.7461 30.9749C37.665 30.7964 37.487 30.6818 37.2909 30.6818C37.0948 30.6818 36.9169 30.7964 36.8357 30.9749L35.0042 35.0042L30.9749 36.8357C30.7964 36.9168 30.6818 37.0948 30.6818 37.2909C30.6818 37.487 30.7964 37.6649 30.9749 37.7461L35.0042 39.5776L36.8357 43.6069C36.9169 43.7854 37.0948 43.9 37.2909 43.9C37.487 43.9 37.665 43.7854 37.7461 43.6069L39.5776 39.5776L43.6069 37.7461C43.7854 37.6649 43.9 37.487 43.9 37.2909C43.9 37.0948 43.7854 36.9168 43.6069 36.8357L39.5776 35.0042L37.7461 30.9749Z"
        fill="white"
        stroke="#090A2C"
        strokeLinejoin="round"
      />
      <mask
        id="mask1_3815_833"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="11"
        y="12"
        width="33"
        height="32"
      >
        <path
          d="M37.2909 24.8182L39.2 20.6182L43.4 18.7091L39.2 16.8L37.2909 12.6L35.3818 16.8L31.1818 18.7091L35.3818 20.6182L37.2909 24.8182ZM27.2364 24.1818L23.4182 15.7818L19.6 24.1818L11.2 28L19.6 31.8182L23.4182 40.2182L27.2364 31.8182L35.6364 28L27.2364 24.1818ZM37.2909 31.1818L35.3818 35.3818L31.1818 37.2909L35.3818 39.2L37.2909 43.4L39.2 39.2L43.4 37.2909L39.2 35.3818L37.2909 31.1818Z"
          fill="url(#paint1_linear_3815_833)"
        />
      </mask>
      <g mask="url(#mask1_3815_833)">
        <path
          d="M37.2909 24.8182L39.2 20.6182L43.4 18.7091L39.2 16.8L37.2909 12.6L35.3818 16.8L31.1818 18.7091L35.3818 20.6182L37.2909 24.8182ZM27.2364 24.1818L23.4182 15.7818L19.6 24.1818L11.2 28L19.6 31.8182L23.4182 40.2182L27.2364 31.8182L35.6364 28L27.2364 24.1818ZM37.2909 31.1818L35.3818 35.3818L31.1818 37.2909L35.3818 39.2L37.2909 43.4L39.2 39.2L43.4 37.2909L39.2 35.3818L37.2909 31.1818Z"
          fill="#292595"
        />
        <g filter="url(#filter1_f_3815_833)">
          <circle cx="35" cy="39.2" r="14" fill="#F3F0D6" fillOpacity="0.4" />
        </g>
        <g filter="url(#filter2_f_3815_833)">
          <circle cx="23.8041" cy="15.3987" r="14" fill="#4FB5FF" />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_f_3815_833"
          x="-12.1667"
          y="-2.83337"
          width="99"
          height="99"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="10.75" result="effect1_foregroundBlur_3815_833" />
        </filter>
        <filter
          id="filter1_f_3815_833"
          x="5"
          y="9.19995"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="8" result="effect1_foregroundBlur_3815_833" />
        </filter>
        <filter
          id="filter2_f_3815_833"
          x="-6.19589"
          y="-14.6013"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="8" result="effect1_foregroundBlur_3815_833" />
        </filter>
        <linearGradient
          id="paint0_linear_3815_833"
          x1="112"
          y1="28.5833"
          x2="56.7778"
          y2="28.5833"
          gradientUnits="userSpaceOnUse"
        >
          <stop />
          <stop offset="1" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_3815_833"
          x1="28"
          y1="11.2"
          x2="28"
          y2="44.8"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D6A65D" />
          <stop offset="1" stopColor="#9A5607" />
        </linearGradient>
      </defs>
    </>
  );

  return (
    <SvgIcon
      ref={ref}
      viewBox="0 0 56 56"
      xmlns="http://www.w3.org/2000/svg"
      sx={[
        {
          fill: 'none',
          width: 56,
          flexShrink: 0,
          height: 'auto',
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      {theme.palette.mode === 'light' ? lightIcon : darkIcon}
    </SvgIcon>
  );
});

export default memo(AiAgentIcon);
