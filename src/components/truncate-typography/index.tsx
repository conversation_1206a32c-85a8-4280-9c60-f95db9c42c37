import type { MouseEvent } from 'react';
import type { TypographyProps } from '@mui/material';

import { useState } from 'react';

import { Tooltip, Typography } from '@mui/material';

interface Props extends TypographyProps {
  text: string;
  maxLength?: number;
}

const TruncateTypography: React.FC<Props> = ({ text, maxLength = Infinity, sx = {}, ...props }) => {
  const [tooltipOpen, setTooltipOpen] = useState(false);

  const isExceedLength = text && text.length > maxLength;
  const displayText = isExceedLength ? `${text.substring(0, maxLength)}...` : text;

  const handleShowTooltip = ({ currentTarget }: MouseEvent<Element>) => {
    if (isExceedLength || currentTarget.scrollWidth > currentTarget.clientWidth) {
      setTooltipOpen(true);
    }
  };

  return (
    <Tooltip
      open={tooltipOpen}
      title={text}
      placement="top"
      arrow
      onClose={() => setTooltipOpen(false)}
    >
      <Typography
        onMouseEnter={handleShowTooltip}
        sx={{ whiteSpace: 'pre-wrap', overflow: 'hidden', textOverflow: 'ellipsis', ...sx }}
        {...props}
      >
        {displayText}
      </Typography>
    </Tooltip>
  );
};

export default TruncateTypography;
