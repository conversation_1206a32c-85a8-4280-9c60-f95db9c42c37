import type { ReactNode } from 'react';

import Dialog from '@mui/material/Dialog';
import { styled } from '@mui/material/styles';


// ----------------------------------------------------------------------

const DialogStyle = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    margin: 0,
    width: '100%',
    maxWidth: 800,
    backgroundColor: 'transparent',
    boxShadow: 'none',
    [theme.breakpoints.down('md')]: {
      maxWidth: '100%',
      height: '100%',
    },
  },
}));

// ----------------------------------------------------------------------

interface ContentDialogProps {
  title?: string;
  open: boolean;
  children: ReactNode;
  onClose: () => void;
}

export function ContentDialog({ children, open, onClose }: ContentDialogProps) {
  return (
    <DialogStyle fullScreen open={open} onClose={onClose}>
      {children}
    </DialogStyle>
  );
} 