import type { SxProps } from '@mui/material/styles';

import { useState, type ReactNode } from 'react';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import { alpha } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { CONFIG } from 'src/global-config';

import { SvgColor } from '../svg-color';

interface ContentPanelProps {
  children: React.ReactNode;
  header?: React.ReactNode;
  isOpen?: boolean;
  onClose?: () => void;
  onMaximize?: () => void;
  contentPanelSx?: object;
  footer?: React.ReactNode;
  onAddNewNote?: () => void;
}

export function ContentPanel({
  children,
  header,
  isOpen,
  onClose,
  onMaximize,
  contentPanelSx = {},
  footer,
  onAddNewNote,
}: ContentPanelProps) {
  const [isMaximized, setIsMaximized] = useState(false);

  const handleMaximize = () => {
    setIsMaximized(!isMaximized);
    onMaximize?.();
  };

  const handleAddNewNote = () => {
    onAddNewNote?.();
  };

  const handleClose = () => {
    onClose?.();
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        marginTop: 4,
        marginBottom: 4,
        flexDirection: 'column',
        bgcolor: (theme) => alpha(theme.palette.background.default, 0.95),
        backdropFilter: 'blur(6px)',
        border: (theme) => `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
      }}
    >
      {/* Header */}
      {header}

      {/* Content */}
      <Stack
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 2,
        }}
      >
        {children}
      </Stack>

      {/* Footer */}
      {footer && (
        <Stack
          sx={{
            px: 2,
            py: 1.5,
            borderTop: (theme) => `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
          }}
        >
          {footer}
        </Stack>
      )}
    </Card>
  );
} 