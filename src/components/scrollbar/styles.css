@import 'simplebar-react/dist/simplebar.min.css';

.simplebar-scrollbar:before {
  background-color: var(--palette-text-disabled);
}
.simplebar-scrollbar.simplebar-visible:before {
  opacity: 0.48;
}

/*
Customize website's scrollbar
*/
.scrollbar {
  overflow: overlay !important;
}

.scrollbar::-webkit-scrollbar {
  background-color: rgba(0, 0, 0, 0);
  width: 8px;
  height: 8px;
  z-index: 1000;
}

.scrollbar::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0);
}

.scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0);
  border-radius: 16px;
  border: 0px solid #fff;
}

.scrollbar::-webkit-scrollbar-button {
  display: none;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #a0a0a5;
}

.scrollbar:hover::-webkit-scrollbar-thumb {
  background-color: #a0a0a5;
}
