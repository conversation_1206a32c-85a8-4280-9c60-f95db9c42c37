import type { LinkProps } from '@mui/material/Link';

import { forwardRef } from 'react';
import { mergeClasses } from 'minimal-shared/utils';

import Link from '@mui/material/Link';
import { styled } from '@mui/material/styles';

import { RouterLink } from 'src/routes/components';

import { logoClasses } from './classes';

// ----------------------------------------------------------------------

export type LogoProps = LinkProps & {
  disabled?: boolean;
};

export const Logo = forwardRef<HTMLAnchorElement, LogoProps>((props, ref) => {
  const { className, href = '/', disabled, sx, ...other } = props;

  const logo = (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_8108_975)">
        <path
          d="M23.2234 17.4198C23.2114 20.8236 20.5269 23.7902 17.2836 23.9853C13.4961 24.2126 10.4827 21.7741 10.0308 18.1564C9.93114 17.3369 9.76163 16.5273 9.52413 15.7366C8.70728 13.0695 7.79551 11.9665 5.01206 12.2286C4.52777 12.2961 4.03645 12.2961 3.55216 12.2286C2.83396 12.088 2.18686 11.7024 1.72138 11.1377C1.2559 10.5729 1.00092 9.86412 1 9.13229C1.00936 7.64966 2.06953 6.32478 3.48264 6.03066C4.21008 5.87396 4.96936 5.97498 5.63053 6.31641C6.2917 6.65785 6.81363 7.21848 7.107 7.90233C7.357 8.47453 7.56823 9.06678 7.86235 9.61491C8.75274 11.27 9.39178 12.0093 11.222 11.8262C12.3423 11.7139 13.4546 11.4144 14.5442 11.1056C19.3571 9.74058 23.2742 13.2673 23.2234 17.4305V17.4198ZM20.698 3.96246C20.6967 3.17628 20.4619 2.40821 20.0234 1.75564C19.585 1.10307 18.9626 0.595402 18.2353 0.297011C17.5079 -0.00138134 16.7083 -0.0770561 15.9379 0.0795822C15.1675 0.23622 14.4609 0.618115 13.9078 1.17684C13.3548 1.73557 12.98 2.44596 12.8312 3.21792C12.6824 3.98989 12.7662 4.78867 13.0719 5.51296C13.3777 6.23725 13.8916 6.85444 14.5486 7.28625C15.2056 7.71806 15.976 7.94505 16.7622 7.93843C17.8111 7.93242 18.8148 7.51031 19.5528 6.76482C20.2907 6.01934 20.7026 5.01142 20.698 3.96246Z"
          fill="url(#paint0_linear_8108_975)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_8108_975"
          x1="3.22462"
          y1="4.43974"
          x2="24.6005"
          y2="16.7834"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#292595" />
          <stop offset="0.07" stopColor="#1E36A2" />
          <stop offset="0.22" stopColor="#0E54B9" />
          <stop offset="0.35" stopColor="#0366C7" />
          <stop offset="0.44" stopColor="#006DCC" />
          <stop offset="0.72" stopColor="#049CF0" />
          <stop offset="1" stopColor="#00E9F5" />
        </linearGradient>
        <clipPath id="clip0_8108_975">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );

  return (
    <LogoRoot
      ref={ref}
      component={RouterLink}
      href={href}
      aria-label="Logo"
      underline="none"
      className={mergeClasses([logoClasses.root, className])}
      sx={[
        () => ({
          width: 40,
          height: 40,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          ...(disabled && { pointerEvents: 'none' }),
        }),
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      {logo}
    </LogoRoot>
  );
});

// ----------------------------------------------------------------------

const LogoRoot = styled(Link)(() => ({
  flexShrink: 0,
  color: 'transparent',
  display: 'inline-flex',
  verticalAlign: 'middle',
}));
