import { memo } from 'react';
import { useParams } from 'react-router';

import { useTranscodingStatusPolling } from 'src/hooks/use-transcoding-status-polling';

import { useGetProjectDetailsQuery } from 'src/store/api/projects';

/**
 * Component that monitors and displays resources that are currently transcoding
 */
const TranscodingStatusMonitor = () => {
  const { id: projectId = '' } = useParams();

  const { data: project } = useGetProjectDetailsQuery(
    { id: projectId! },
    {
      skip: !projectId,
    }
  );

  // Use the custom hook to poll for transcoding status updates
  useTranscodingStatusPolling({
    projectId,
    resources: project?.resources ?? [],
  });

  return null;
};

export default memo(TranscodingStatusMonitor);
