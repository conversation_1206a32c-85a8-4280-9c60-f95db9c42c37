import { toast } from 'sonner';
import { debounce } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import { useMemo, useState, useEffect, useCallback } from 'react';

import Stack from '@mui/material/Stack';
import { alpha } from '@mui/material/styles';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';

import { CONFIG } from 'src/global-config';
import { useAppSelector } from 'src/store';
import { addNote, selectNote } from 'src/store/slices/notes/slice';
import { selectFocusedResource } from 'src/store/slices/resources/selectors';
import { selectNotes, selectSelectedNote } from 'src/store/slices/notes/selectors';
import { useCreateNoteMutation, useUpdateNoteMutation } from 'src/store/api/notes';

import useConvertToResource from 'src/sections/resources/hooks/convert-to-resource';

import { Editor } from '../editor';
import { Iconify } from '../iconify';
import { NoteList } from './note-list';
import { SvgColor } from '../svg-color';
import { ContentPanel } from '../content-panel';

import type { NoteItem as NoteItemType } from './types';

interface NotesProps {
  isOpen?: boolean;
  onClose?: () => void;
  onMaximize?: () => void;
  isCanEdit?: boolean;
}

export function Notes({ isOpen, onClose, onMaximize, isCanEdit = true }: NotesProps) {
  const [editorContent, setEditorContent] = useState<string>('');
  const [editorTitle, setEditorTitle] = useState<string>('');
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingContent, setIsEditingContent] = useState(false);
  const [createNote, { isLoading: isCreatingNote }] = useCreateNoteMutation();
  const [updateNote, { isLoading: isUpdatingNote }] = useUpdateNoteMutation();
  const notes = useAppSelector(selectNotes);
  const selectedNote = useAppSelector(selectSelectedNote);
  const dispatch = useDispatch();
  const focusedResource = useSelector(selectFocusedResource);
  const { convertNoteToResource, isConverting } = useConvertToResource();

  const isDirty = useMemo(
    () => selectedNote?.content !== editorContent || selectedNote?.title !== editorTitle,
    [selectedNote, editorContent, editorTitle]
  );

  const handleSelectNote = (note: NoteItemType) => {
    dispatch(selectNote(note));
    setEditorContent(note.content);
    setEditorTitle(note.title);
  };

  const saveNote = useCallback(
    (content: string, title: string) => {
      if (selectedNote && (content !== selectedNote.content || title !== selectedNote.title)) {
        updateNote({
          id: selectedNote.id,
          payload: {
            content,
            title,
            resourceId: focusedResource?.id,
          },
        })
          .catch((err) => {
            toast.error('Failed to save note');
          })
          .finally(() => {
            setIsEditingContent(false);
            setIsEditingTitle(false);
          });
      }
    },
    [selectedNote, focusedResource?.id, updateNote]
  );

  const debouncedSaveNote = useMemo(
    () =>
      debounce((content: string, title: string) => {
        setEditorContent(content);
        setEditorTitle(title);
        saveNote(content, title);
      }, 1000),
    [saveNote]
  );

  const handleEditorChange = useCallback(
    (content: string) => {
      setIsEditingContent(true);
      debouncedSaveNote(content, editorTitle);
    },
    [debouncedSaveNote, editorTitle]
  );

  const handleTitleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      // limit title length to 40 characters
      setIsEditingTitle(true);
      if (event.target.value.length > 40) {
        toast.error('Title cannot be longer than 40 characters');
        return;
      }
      setEditorTitle(event.target.value);
      debouncedSaveNote(editorContent, event.target.value);
    },
    [debouncedSaveNote, editorContent]
  );

  const handleTitleBlur = useCallback(() => {
    setIsEditingTitle(false);
    debouncedSaveNote.flush();
  }, [debouncedSaveNote]);

  const handleTitleClick = useCallback(() => {
    setIsEditingTitle(true);
  }, []);

  const handleBack = useCallback(() => {
    dispatch(selectNote(null));
    setEditorContent('');
    setEditorTitle('');
    setIsEditingTitle(false);
  }, []);

  const handleConvertToSource = useCallback(async () => {
    convertNoteToResource(editorContent, editorTitle);
  }, [editorContent, editorTitle, convertNoteToResource]);

  const handleAddNewNote = useCallback(() => {
    createNote({
      payload: {
        content: 'Edit this note',
        title: 'New Note',
        resourceId: focusedResource?.id,
      },
    })
      .then((res) => {
        if (res.data) {
          handleSelectNote(res.data);
          dispatch(addNote(res.data));
        }
      })
      .catch((err) => {
        toast.error('Failed to add new note');
      });
  }, [createNote, focusedResource?.id, handleSelectNote]);

  useEffect(() => debouncedSaveNote.cancel(), [debouncedSaveNote]);
  useEffect(() => {
    if (selectedNote) {
      setEditorContent(selectedNote.content);
      setEditorTitle(selectedNote.title);
    }
  }, [selectedNote]);

  const EditorFooter = useCallback(
    () => (
      <Stack direction="row" justifyContent="space-between" spacing={2} width="100%">
        <Stack>
          <Typography variant="body2">
            {isEditingContent || isEditingTitle ? 'Saving...' : 'Saved'}
          </Typography>
        </Stack>
        <LoadingButton
          variant="contained"
          onClick={handleConvertToSource}
          color="primary"
          loading={isConverting}
        >
          {isConverting ? 'Converting...' : 'Convert to Source'}
        </LoadingButton>
      </Stack>
    ),
    [handleBack, handleConvertToSource, isConverting]
  );

  const PanelHeader = (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{
        px: 2,
        py: 1.5,
        borderBottom: (theme) => `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
      }}
    >
      <Stack direction="row" alignItems="center" spacing={2}>
        {selectedNote ? (
          <>
            <IconButton size="small" onClick={handleBack}>
              <Iconify width={24} height={24} icon="eva:arrow-back-fill" />
            </IconButton>
            <Typography
              variant="h6"
              sx={{
                color: 'text.primary',
              }}
            >
              Notes →
            </Typography>
            {isEditingTitle ? (
              <TextField
                value={editorTitle}
                onChange={handleTitleChange}
                onBlur={handleTitleBlur}
                autoFocus
                variant="standard"
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: '1.25rem',
                    fontWeight: 600,
                  },
                }}
              />
            ) : (
              <Typography
                variant="h6"
                sx={{
                  color: 'text.primary',
                  cursor: 'pointer',
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
                onClick={handleTitleClick}
              >
                {editorTitle}
              </Typography>
            )}
          </>
        ) : (
          <Typography variant="h6" sx={{ color: 'text.primary' }}>
            Notes
          </Typography>
        )}
      </Stack>
      <Stack direction="row" spacing={1}>
        <IconButton size="small" onClick={onClose}>
          <SvgColor src={`${CONFIG.assetsDir}/assets/icons/files/ic-exit.svg`} />
        </IconButton>
      </Stack>
    </Stack>
  );

  return (
    <ContentPanel
      header={PanelHeader}
      isOpen={isOpen}
      onClose={onClose}
      onMaximize={onMaximize}
      contentPanelSx={selectedNote ? { p: 0 } : {}}
      footer={selectedNote && isCanEdit ? <EditorFooter /> : undefined}
      onAddNewNote={handleAddNewNote}
    >
      {selectedNote ? (
        isCanEdit ? (
          <Editor
            slotProps={{ wrapper: { sx: { height: '100%' } } }}
            sx={{ height: '100%' }}
            value={editorContent}
            onChange={handleEditorChange}
          />
        ) : (
          <div dangerouslySetInnerHTML={{ __html: editorContent }} />
        )
      ) : (
        <NoteList notes={notes} onSelectNote={handleSelectNote} />
      )}
    </ContentPanel>
  );
}
