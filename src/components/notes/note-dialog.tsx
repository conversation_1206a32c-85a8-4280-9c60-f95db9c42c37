import { Notes } from './notes';
import { ContentDialog } from '../content-panel';

// ----------------------------------------------------------------------

interface NoteDialogProps {
  open: boolean;
  onClose: () => void;
  isCanEdit?: boolean;
}

export function NoteDialog({ open, onClose, isCanEdit = true }: NoteDialogProps) {
  return (
    <ContentDialog title="Notes" open={open} onClose={onClose}>
      <Notes isOpen={open} onClose={onClose} isCanEdit={isCanEdit} />
    </ContentDialog>
  );
}
