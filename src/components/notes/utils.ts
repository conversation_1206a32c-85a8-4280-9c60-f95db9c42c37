
const firstElementRegex = /<[^>]*>([^<>]*)<\/[^>]*>/;
const allTagsRegex = /<[^>]*>([^<>]*)<\/[^>]*>/g;


const hasMoreThanOneElement = (content: string) => {
    const htmlTags = content.match(allTagsRegex);
    return htmlTags && htmlTags.length > 1;
}

export const truncateNoteContent = (content: string, maxLength: number = 40) => {
    const isMore = hasMoreThanOneElement(content);
    // get the first element of the content, such as <p></p>
    const firstElement = content.match(firstElementRegex)
    if (firstElement) {
        const firstElementContent = firstElement[1];
        if (firstElementContent.length <= maxLength) {
            return firstElementContent.replace(/<[^>]*>/g, '') + (isMore ? "..." : "");
        }
        else {
            return firstElementContent.replace(/<[^>]*>/g, '').substring(0, maxLength) + "...";
        }
    }
    return firstElement || content
};


