import type { SxProps } from '@mui/material';

import { Box, useTheme } from '@mui/material';

const AnimateDots: React.FC<{
  size?: number;
  color?: string;
  sx?: SxProps;
}> = ({ size = 12, color, sx = {} }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        '& > div': {
          width: size,
          height: size,
          margin: '2px 4px',
          backgroundColor: color ?? theme.palette.grey[500],
          borderRadius: '50%',
          animation: 'bouncing-loader 0.4s infinite alternate',
        },
        '& > div:nth-child(2)': {
          animationDelay: '0.1s',
        },
        '& > div:nth-child(3)': {
          animationDelay: '0.2s',
        },
        '@keyframes bouncing-loader': {
          to: {
            opacity: 0.1,
            transform: `translateY(${size}px)`,
          },
        },
        ...sx,
      }}
    >
      <div />
      <div />
      <div />
    </Box>
  );
};

export default AnimateDots;
