import type { UserFeedbackEntityType} from "src/store/api/user-feedback/types";

import { useState, useCallback } from "react";

import { Card, Stack, Button, Select, MenuItem, Container, TextField, InputLabel, Typography, FormControl } from "@mui/material";

import { UserFeedbackRating } from "src/store/api/user-feedback/types";
import { useSubmitFeedbackDetailsMutation } from "src/store/api/user-feedback/hooks";

import { LoadingScreen } from "../loading-screen";
import { CustomAlert } from "../alert/custom-alert";

function FeedbackDetailsForm({ entityId, entityType, userId, setStatus }: {
    entityId: string;
    entityType: UserFeedbackEntityType;
    userId: string;
    setStatus: (status: 'loading' | 'error' | 'success') => void;
  }) {
    const [reason, setReason] = useState('');
    const [customReason, setCustomReason] = useState('');
    const [comment, setComment] = useState('');
    const [submitFeedbackDetails, { isLoading: isLoadingDetails }] = useSubmitFeedbackDetailsMutation();
    const [detailsStatus, setDetailsStatus] = useState<'idle' | 'success' | 'error'>('idle');
    const [detailsError, setDetailsError] = useState('');
  
    const reasonOptions = [
      'Too generic',
      'Missed key point',
      'Too long',
      'Wrong tone',
      'Other',
    ];
  
    const handleSubmitFeedbackDetails = useCallback(async () => {
      try {
        await submitFeedbackDetails({
          payload: {
            userId,
            entityId,
            entityType,
            rating: UserFeedbackRating.ThumbDown,
            reason: reason === 'Other' ? customReason : reason,
            comment,
          },
        }).unwrap();
        setDetailsStatus('success');
        setStatus('success');
      } catch (error) {
        setDetailsStatus('error');
        setDetailsError('Failed to submit feedback details. Please try again.');
      }
    }, [userId, entityId, entityType, reason, customReason, comment, submitFeedbackDetails, setStatus]);
  
    if (!entityId || !entityType || !userId) {
      return <CustomAlert severity="error" title="Error">Invalid resource link. Missing required parameters.</CustomAlert>;
    }
  
    if (isLoadingDetails) {
      return <LoadingScreen />;
    }
  
    if (detailsStatus === 'success') {
      return (
        <Container maxWidth="sm" sx={{ py: 10 }}>
          <Card sx={{ p: 5 }}>
            <Stack spacing={3} alignItems="center">
              <CustomAlert severity="success" title="Success">
                Thank you for your feedback!
              </CustomAlert>
            </Stack>
          </Card>
        </Container>
      );
    }
  
    const isSubmitDisabled =
      isLoadingDetails ||
      !reason ||
      (reason === 'Other' && !customReason) ||
      !comment;
  
    return (
      <Container maxWidth="sm" sx={{ py: 10 }}>
        <Card sx={{ p: 5 }}>
          <Stack spacing={3} alignItems="center">
            <Typography variant="h4">Feedback Details</Typography>
            <FormControl fullWidth>
              <InputLabel id="reason-label">What went wrong?</InputLabel>
              <Select
                labelId="reason-label"
                value={reason}
                label="What went wrong?"
                onChange={(e) => setReason(e.target.value)}
              >
                {reasonOptions.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            {reason === 'Other' && (
              <TextField
                label="Please specify"
                multiline
                rows={2}
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                fullWidth
              />
            )}
            <TextField
              label="Feedback"
              multiline
              rows={4}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              fullWidth
            />
            {detailsStatus === 'error' && (
              <CustomAlert severity="error" title="Error">{detailsError}</CustomAlert>
            )}
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmitFeedbackDetails}
              disabled={isSubmitDisabled}
            >
              {isLoadingDetails ? 'Submitting...' : 'Submit'}
            </Button>
          </Stack>
        </Card>
      </Container>
    );
  }

  export default FeedbackDetailsForm;