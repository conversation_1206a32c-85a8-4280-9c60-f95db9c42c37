import type { ReactNode } from "react";

import { Alert, AlertTitle } from "@mui/material";

interface CustomAlertProps {
  severity: 'success' | 'error';
  title: string;
  children: ReactNode;
}

export function CustomAlert({ severity, title, children }: CustomAlertProps) {
  return (
    <Alert severity={severity} sx={{ width: '100%' }}>
      <AlertTitle>{title}</AlertTitle>
      {children}
    </Alert>
  );
}