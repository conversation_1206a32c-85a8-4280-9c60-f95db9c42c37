import { themeConfig } from 'src/theme/theme-config';

import type { SettingsState } from './types';

// ----------------------------------------------------------------------

export const SETTINGS_STORAGE_KEY: string = 'app-settings';

export const defaultSettings: SettingsState = {
  colorScheme: themeConfig.defaultMode,
  primaryColor: 'default',
  fontSize: 16,
  fontFamily: themeConfig.fontFamily.primary,
  direction: themeConfig.direction,
  contrast: 'default',
  navLayout: 'mini',
  navColor: 'integrate',
  compactLayout: false,
};
