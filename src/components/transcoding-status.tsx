import { Box, keyframes, Typography } from '@mui/material';

/**
 * A component that shows an animated indicator for transcoding in progress
 */
const TranscodingStatus = () => {
  const ping = keyframes`
    75%, 100% {
      transform: scale(2);
      opacity: 0;
    }
  `;

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Box sx={{ position: 'relative', display: 'flex', height: 12, width: 12 }}>
        <Box
          sx={{
            position: 'absolute',
            inset: 0,
            borderRadius: '50%',
            bgcolor: 'info.light',
            opacity: 0.75,
            animation: `${ping} 1.5s cubic-bezier(0, 0, 0.2, 1) infinite`,
          }}
        />
        <Box
          sx={{
            position: 'relative',
            display: 'inline-flex',
            borderRadius: '50%',
            height: 12,
            width: 12,
            bgcolor: 'info.main',
          }}
        />
      </Box>
      <Typography variant="caption" sx={{ color: 'info.main', fontWeight: 600 }}>
        Transcoding in progress
      </Typography>
    </Box>
  );
};

export default TranscodingStatus;
