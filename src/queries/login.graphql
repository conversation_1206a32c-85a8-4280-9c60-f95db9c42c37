fragment resourceInfo on Resource {
  name
  url
  fileSize
  duration
  originalResourceId
  originalResourceState
}
fragment user on User {
  id
  name
  rootOrgId
  email
  socialLogin
  avatarUrl
  referralById
  currentOrgId
  theme
  socialLogin
  themeWarmness
  studioSettings
  avatarUrl
  blockedBySystemAdmin
  autoRecordMeeting
}
fragment authInfo on AuthSession {
  id
  secret
  user {
    ...user
  }
}
mutation login($email: Email!, $password: NeString!, $dns: String, $captchaToken: String) {
  login(email: $email, password: $password, dns: $dns, captchaToken: $captchaToken) {
    ...authInfo
  }
}
mutation rnLogin($email: Email!, $password: NeString!) {
  login(email: $email, password: $password) {
    ...authInfo
  }
}
